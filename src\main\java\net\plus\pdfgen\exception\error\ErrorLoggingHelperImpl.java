package net.plus.pdfgen.exception.error;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

@Component
@Slf4j
public class ErrorLoggingHelperImpl implements ErrorLoggingHelper
{
    private static final String REQUEST_LOGGING_NOT_ENABLED_MESSAGE
            = "REQUEST LOGGING NOT ENABLED AT DEBUG OR INFO LEVEL";

    private final boolean includeQueryString;
    private final boolean includeClientInfo;
    private final boolean includeHeaders;

    public ErrorLoggingHelperImpl(
            @Value("${logging.include-query-string}")
                    boolean includeQueryString,
            @Value("${logging.include-client-info}")
                    boolean includeClientInfo,
            @Value("${logging.include-headers}")
                    boolean includeHeaders
    )
    {
        this.includeQueryString = includeQueryString;
        this.includeClientInfo = includeClientInfo;
        this.includeHeaders = includeHeaders;
    }

    public String getRequestDetails(HttpServletRequest request)
    {
        return shouldLog() ? createMessage(request) : REQUEST_LOGGING_NOT_ENABLED_MESSAGE;
    }

    private boolean shouldLog()
    {
        return log.isDebugEnabled();
    }

    /*
     * Copied from org.springframework.web.filterAbstractRequestLoggingFilter as that was a protected method.
     * Thought about extending the class, but this isn't really being used as a Filter, and didn't semantically feel
     * right to extend it. Plus, the filter has some additional processing logic which could introduce risks when
     * upgrading the framework. Note - due to the way the Request Body can only be consumed once, I don't think
     * we have access to include it in the payload logging here, even though that could potentially be useful.
     */
    private String createMessage(HttpServletRequest request)
    {
        StringBuilder msg = new StringBuilder();
        msg.append(request.getMethod()).append(" ");
        msg.append(request.getRequestURI());

        if (includeQueryString)
        {
            String queryString = request.getQueryString();
            if (queryString != null)
            {
                msg.append('?').append(queryString);
            }
        }

        if (includeClientInfo)
        {
            String client = request.getRemoteAddr();
            if (StringUtils.hasLength(client))
            {
                msg.append(", client=").append(client);
            }
            HttpSession session = request.getSession(false);
            if (session != null)
            {
                msg.append(", session=").append(session.getId());
            }
            String user = request.getRemoteUser();
            if (user != null)
            {
                msg.append(", user=").append(user);
            }
        }

        if (includeHeaders)
        {
            HttpHeaders headers = new ServletServerHttpRequest(request).getHeaders();
            msg.append(", headers=").append(headers);
        }

        return msg.toString();
    }
}
