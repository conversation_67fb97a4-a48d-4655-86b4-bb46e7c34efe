package net.plus.pdfgen.controller;


import com.fasterxml.jackson.databind.ObjectMapper;
import net.plus.pdfgen.controller.DeliveryCommsController;
import net.plus.pdfgen.controller.PCICSController;
import net.plus.pdfgen.model.DeliveryCommsDto;
import net.plus.pdfgen.model.PCICSStatusDto;
import net.plus.pdfgen.service.DeliveryCommsService;
import net.plus.pdfgen.service.PCICSService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


@SpringBootTest
@RunWith(SpringRunner.class)
@ContextConfiguration(classes = {DeliveryCommsController.class, PCICSController.class})
@AutoConfigureMockMvc
@ActiveProfiles("test")
public class DeliveryCommsControllerTest {

    @MockBean
    private DeliveryCommsService deliveryCommsService;

    @MockBean
    private PCICSService pcicsService;
    private MockMvc mockMvc;

    private MockMvc mockMvc1;

    String streamingId = "0a49af44fb126c85d18e54a7a3fbd959";

    @Before
    public void setUp()
    {
        final DeliveryCommsController deliveryCommsController = new DeliveryCommsController(deliveryCommsService);
        final PCICSController pcicsController = new PCICSController(pcicsService);
        this.mockMvc = MockMvcBuilders.standaloneSetup(deliveryCommsController).build();
        this.mockMvc1 = MockMvcBuilders.standaloneSetup(pcicsController).build();
    }

    @Test
    public void updateDeliveryCommsStatusTestSuccess() throws Exception {
        DeliveryCommsDto deliveryCommsDto = new DeliveryCommsDto();
        deliveryCommsDto.setDeliveryType("EMAIL");
        deliveryCommsDto.setStatus("SUCCESS");
        deliveryCommsDto.setHandle("PCICS");
        deliveryCommsDto.setOrderId("629ad7bc-4e55-40e5-b9d6-5e07459786df");

        this.mockMvc.perform(MockMvcRequestBuilders.put("/pdfgen/v1/pcics/delivery-comms/{streamingId}",
                                                        new ObjectMapper().writeValueAsString(deliveryCommsDto))
                                                   .param("streamingId",streamingId)
                                                   .content(new ObjectMapper().writeValueAsString(deliveryCommsDto))
                                                   .contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON)).andExpect(status().isOk());
    }

    @Test
    public void updateDeliveryCommsStatusTestFailure() throws Exception {
        DeliveryCommsDto deliveryCommsDto = new DeliveryCommsDto();
        deliveryCommsDto.setDeliveryType("EMAIL");
        deliveryCommsDto.setStatus("SUCCESS");
        deliveryCommsDto.setHandle("PCICS");
        deliveryCommsDto.setOrderId("629ad7bc-4e55-40e5-b9d6-5e07459786df");

        String streamingId = "0a49af44fb126c85d18e54a7a3fbd959";


        mockMvc.perform(put("http://localhost:8089/pdfgen/v1/pcics/delivery-comms/{streamingId}/",deliveryCommsDto).param("streamingId", streamingId))
               .andExpect(status().is4xxClientError()).andReturn();
    }

    @Test
    public void retrievePdfTestSucess() throws Exception {

        this.mockMvc1.perform(get("http://localhost:8089/pdfgen/v1/pcics/{streamingId}",streamingId)
                                      .param("streamingId",streamingId)
                                      .contentType(MediaType.APPLICATION_JSON)
                                      .accept(MediaType.APPLICATION_JSON)).andExpect(status().isOk());
    }

    @Test
    public void updateJourneyCompletionStatusTestSuccess()throws Exception{
        PCICSStatusDto statusDto=new PCICSStatusDto();
        statusDto.setAccountId(1212121);
        statusDto.setOrderId("order");

        this.mockMvc1.perform(put("/pdfgen/v1/pcics/{streamingId}",streamingId)
                                      .content(new ObjectMapper().writeValueAsString(statusDto))
                                      .contentType(MediaType.APPLICATION_JSON)
                                      .accept(MediaType.APPLICATION_JSON)).andExpect(status().isOk());
    }

    @Test
    public void updateJourneyCompletionStatusTestFailure()throws Exception{
        PCICSStatusDto statusDto=new PCICSStatusDto();
        statusDto.setAccountId(1212121);
        statusDto.setOrderId("order");

        this.mockMvc1.perform(put("/pdfgen/v1/pcics/{streamingId}",statusDto)
                                      .contentType(MediaType.APPLICATION_JSON)
                                      .accept(MediaType.APPLICATION_JSON)).andExpect(status().is4xxClientError());
    }
}
