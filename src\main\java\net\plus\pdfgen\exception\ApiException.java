package net.plus.pdfgen.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public abstract class ApiException extends RuntimeException
{
    private final String code;

    private int statusCode;

    private String streamingId;

    ApiException(String code, String message, String streamingId)
    {
        super(message);
        this.code = code;
        this.streamingId = streamingId;
    }

    ApiException(String code, String message, int statusCode)
    {
        super(message);
        this.code = code;
        this.statusCode = statusCode;
    }

    ApiException(String code, String message, String streamingId, Throwable cause)
    {
        super(message, cause);
        this.code = code;
        this.streamingId = streamingId;
    }
}
