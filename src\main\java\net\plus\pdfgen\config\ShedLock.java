package net.plus.pdfgen.config;

import javax.sql.DataSource;

import net.javacrumbs.shedlock.core.LockProvider;
import net.javacrumbs.shedlock.provider.jdbctemplate.JdbcTemplateLockProvider;
import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;

@Configuration
@EnableScheduling
@EnableSchedulerLock(defaultLockAtMostFor = "${data.retention.pcics-pdf-cleanup-default-lock-at-most-for}")
public class ShedLock
{
    @Bean
    public LockProvider lockProvider(
            @Qualifier("appdbDataSource")
                    DataSource dataSource)
    {
        return new JdbcTemplateLockProvider(
                JdbcTemplateLockProvider.Configuration.builder()
                                                      .withTableName("shedlock")
                                                      .withJdbcTemplate(new JdbcTemplate(dataSource))
                                                      .usingDbTime()
                                                      .build());
    }
}
