coredb-datasource.jdbcurl=jdbc:h2:mem:pdfgen;DB_CLOSE_DELAY=-1;INIT=RUNSCRIPT FROM 'classpath:schema_h2.sql';
coredb-datasource.DriverClassName=org.h2.Driver
coredb-datasource.username=sa
coredb-datasource.password=
spring.h2.console.enabled=true
spring.jpa.show-sql=true

#APPDB configuration
appdb-datasource.DriverClassName=org.h2.Driver
appdb-datasource.jdbcUrl=jdbc:h2:mem:pdfgen;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;INIT=RUNSCRIPT FROM 'classpath:appdb_pdfgen_schema.sql'
appdb-datasource.username=sa
appdb-datasource.password=

spring.main.allow-bean-definition-overriding=true

pcics.dir.location=./src/test/resources/
pcics.file.location = ${pcics.dir.location}/pcics/
pcics.stream.url=http://localhost:8089/pdfgen/v1/pcics


#Loging config
logging.level.net.plus.pdfgen=DEBUG