package net.plus.pdfgen.dao;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import javax.sql.DataSource;

import net.plus.pdfgen.exception.SqlExecException;
import net.plus.pdfgen.exception.StreamingIdNotFoundExeception;
import net.plus.pdfgen.exception.error.ErrorCode;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

import net.plus.pdfgen.constant.PdfStatus;
import net.plus.pdfgen.model.DeliveryCommsDto;
import net.plus.pdfgen.model.PCICSRequestDto;
import net.plus.pdfgen.model.PCICSStatusDto;

@Component
@Slf4j
public class PCICSDao
{
    private static final String STREAMING_ID = "streaming_id";
    private static final String ORDER_ID = "order_id";
    private static final String STATUS_ID = "status_id";
    private static final String CURRENT_DATE = "current_date";
    private static final String PCICS_TOGGLE = "pcics_toggle";

    private final DataSource coreDataSource;
    private final JdbcTemplate jdbcTemplate;
    private final NamedParameterJdbcTemplate npJdbcTemplate;

    @Value("${pcics.info.select.filePath.query}")
    private String pcicsInfoSelectQuery;

    @Value("${pcics.info.status.update.query}")
    private String pcicsInfoUpdateQuery;

    @Value("${delivery.info.comms.insert.or.update.query}")
    private String deliveryCommsInsertOrUpdateQuery;

    @Value("${pcics.info.insert.or.update.query}")
    private String pcicsInsertOrUpdatePCICSInfoRecordQuery;

    @Value("${pcics.info.streamingid.exists.query}")
    private String streamingIdExistsQuery;

    @Value("${pcics.info.archival.eligible.pdfs.query}")
    private String archivalEligiblePdfsQuery;

    @Value("${pcics.info.delete.pdfs.query}")
    private String archivalDeletePdfsQuery;

    @Value("${pcics.info.update.status.query}")
    private String archivalUpdatePdfStatus;

    @Value("${delivery.info.archival.delete.entries.query}")
    private String archivalDeliveryInfoDeleteQuery;

    @Value("${data.retention.pcics-pdf-cleanup-max-age-in-years}")
    private String ageInYears;

    @Value("${data.retention.pcics-invalid-pdf-cleanup-max-age-in-days}")
    private String ageInDays;

    @Value(value = "${number.of.records.to.fetch}")
    private int fetchSize;

    @Value("${pcics.select.contenttoggledate.query}")
    private String pcicsNewContentToggleDateQuery;

    public PCICSDao(
            @Qualifier("coredbDataSource")
                    DataSource coreDataSource)
    {
        this.coreDataSource = coreDataSource;
        this.jdbcTemplate = new JdbcTemplate(this.coreDataSource);
        this.jdbcTemplate.setFetchSize(fetchSize);
        this.npJdbcTemplate = new NamedParameterJdbcTemplate(jdbcTemplate);
    }

    public boolean isStreamingIdExist(String streamingId)
    {
        log.debug("Entry: PCICSDao.isStreamingIdExist()");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(STREAMING_ID, streamingId);
        try
        {
            int count = Objects.requireNonNull(this.npJdbcTemplate).queryForObject(streamingIdExistsQuery,
                                                                                   parameters,
                                                                                   Integer.class);
            log.debug("Exit: PCICSDao.isStreamingIdExist()");
            return count == 1;
        }
        catch (DataAccessException e)
        {
            throw new SqlExecException(ErrorCode.COREDB_QUERY_EXEC_FAILURE,
                                       String.format(
                                               "Error occurred while executing isStreamingIdExist, Query = [%s], Params = [%s]",
                                               streamingIdExistsQuery,
                                               Arrays.toString(parameters.entrySet().toArray())),
                                       streamingId,
                                       e);
        }
    }

    public void insertOrUpdatePcicsInfoRecord(PCICSRequestDto requestDto,
            String streamingId,
            String filePath,
            int status)
    {
        log.debug("Entry: PCICSDao.insertOrUpdatePcicsInfoRecord()");

        Map<String, Object> parameters = new HashMap<>();
        parameters.put(STREAMING_ID, streamingId);
        parameters.put("account_id", requestDto.getAccountId());
        parameters.put(ORDER_ID, requestDto.getOrderId());
        parameters.put("customer_type", requestDto.getCustomerType().toString());
        parameters.put("file_path", filePath);
        parameters.put(STATUS_ID, status);

        String errorMessage = String.format(
                "Error occurred while executing insertOrUpdatePcicsInfoRecord, Query = [%s], Params = [%s]",
                pcicsInsertOrUpdatePCICSInfoRecordQuery,
                Arrays.toString(parameters.entrySet().toArray()));
        executeUpdateQuery(pcicsInsertOrUpdatePCICSInfoRecordQuery, parameters, streamingId, errorMessage);

        log.debug("Exit: PCICSDao.insertOrUpdatePcicsInfoRecord()");
    }

    public String retrieveFilePath(String streamingId)
    {
        log.debug("Entry: PCICSDao.retrieveFilePath()");
        String filePath;
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(STREAMING_ID, streamingId);

        try
        {
            filePath = Objects.requireNonNull(this.npJdbcTemplate).queryForObject(pcicsInfoSelectQuery,
                                                                                  parameters,
                                                                                  String.class);
            log.debug("Exit: PCICSDao.retrieveFilePath(). Successfully retrieved filePath = {} ", filePath);
        }
        catch (EmptyResultDataAccessException e)
        {
            throw new StreamingIdNotFoundExeception(ErrorCode.STREAMING_ID_NOT_FOUND,
                                                    "retrieveFilePath : Unable to find input streamingId in pcics_info",
                                                    streamingId,
                                                    e);
        }
        catch (NullPointerException | DataAccessException e)
        {
            throw new SqlExecException(ErrorCode.COREDB_QUERY_EXEC_FAILURE,
                                       String.format(
                                               "retrieveFilePath : Error occurred while executing query to retrieve file path of PDF, Query = [%s], Params = [%s]",
                                               pcicsInfoSelectQuery,
                                               Arrays.toString(parameters.entrySet().toArray())),
                                       streamingId,
                                       e);
        }
        return filePath;
    }

    public void updatePCICSInfoRecord(String streamingId, PCICSStatusDto statusDto)
    {
        log.debug("Entry: PCICSDao.updatePCICSInfoRecord()");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("account_id", statusDto.getAccountId());
        parameters.put(ORDER_ID, statusDto.getOrderId());
        parameters.put(STREAMING_ID, streamingId);
        parameters.put(STATUS_ID, PdfStatus.COMPLETED.getStatusId());

        String errorMessage = String.format(
                "Error occurred while executing updatePCICSInfoRecord, Query = [%s], Params = [%s]",
                pcicsInfoUpdateQuery,
                Arrays.toString(parameters.entrySet().toArray()));
        executeUpdateQuery(pcicsInfoUpdateQuery, parameters, streamingId, errorMessage);

        log.debug("Exit: PCICSDao.updatePCICSInfoRecord()");
    }

    public void insertOrUpdateDeliveryCommsDetails(String streamingId, DeliveryCommsDto deliveryCommsDto)
    {
        log.debug("Entry: PCICSDao.insertOrUpdateDeliveryCommsDetails()");

        Map<String, Object> parameters = new HashMap<>();
        parameters.put(STREAMING_ID, streamingId);
        parameters.put(ORDER_ID, deliveryCommsDto.getOrderId());
        parameters.put("delivery_type", deliveryCommsDto.getDeliveryType());
        parameters.put("handle", deliveryCommsDto.getHandle());
        parameters.put("status", deliveryCommsDto.getStatus());

        String errorMessage = String.format(
                "Error occurred while executing insertOrUpdateDeliveryCommsDetails, Query = [%s], Params = [%s]",
                deliveryCommsInsertOrUpdateQuery,
                Arrays.toString(parameters.entrySet().toArray()));
        executeUpdateQuery(deliveryCommsInsertOrUpdateQuery, parameters, streamingId, errorMessage);

        log.debug("Exit: PCICSDao.insertOrUpdateDeliveryCommsDetails()");
    }

    private int executeUpdateQuery(String query,
            Map<String, Object> parameters,
            String streamingId,
            String errorMessage)
    {
        try
        {
            return Objects.requireNonNull(this.npJdbcTemplate).update(query, parameters);
        }
        catch (NullPointerException | DataAccessException e)
        {
            throw new SqlExecException(ErrorCode.COREDB_QUERY_EXEC_FAILURE, errorMessage, streamingId, e);
        }
    }

    public Optional<Map<String, String>> retrieveArchivalEligiblePdfs()
    {
        log.debug("Entry: PCICSDao.retrieveArchivalEligiblePdfs()");

        Map<String, String> eligiblePdfs = new HashMap<>();
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("ageInYears", ageInYears);
        parameters.put("ageInDays", ageInDays);

        try
        {
            List<Map<String, Object>> pdfList = Objects.requireNonNull(this.npJdbcTemplate).queryForList(
                    archivalEligiblePdfsQuery, parameters);
            if (!pdfList.isEmpty())
            {
                pdfList.forEach(rowMap -> {
                    String streamingId = (String) rowMap.get(STREAMING_ID);
                    String filePath = (String) rowMap.get("file_path");
                    eligiblePdfs.put(streamingId, filePath);
                });
            }

            log.trace("retrieveArchivalEligiblePdfs : List of Pdfs considered for Archival are : {}",
                      eligiblePdfs.values());

            log.debug("Exit: PCICSDao.retrieveArchivalEligiblePdfs()");
        }
        catch (NullPointerException | DataAccessException e)
        {
            throw new SqlExecException(ErrorCode.COREDB_QUERY_EXEC_FAILURE,
                                       String.format(
                                               "retrieveArchivalEligiblePdfs : Error occurred while executing query to retrieve list of eligible pdfs for archival, Query = [%s], Params = [%s]",
                                               archivalEligiblePdfsQuery,
                                               Arrays.toString(parameters.entrySet().toArray())),
                                       null,
                                       e);
        }
        return Optional.of(eligiblePdfs);
    }

    public void deleteFromPCICSInfo(PdfStatus pdfStatus)
    {
        log.debug("Entry: PCICSDao.deleteFromPCICSInfoWithStatusDeleted()");

        Map<String, Object> parameters = new HashMap<>();
        parameters.put(STATUS_ID, pdfStatus.getStatusId());

        String errorMessage = String.format(
                "Error occurred while executing deleteFromPCICSInfoWithStatusDeleted, Query = [%s], Params = [%s]",
                archivalDeletePdfsQuery,
                Arrays.toString(parameters.entrySet().toArray()));

        int count = executeUpdateQuery(archivalDeletePdfsQuery, parameters, null, errorMessage);

        log.info("Deleted {} entries from pcics_info table successfully", count);
        log.debug("Exit: PCICSDao.deleteFromPCICSInfoWithStatusDeleted()");
    }

    public void deleteFromDeliveryInfo(PdfStatus pdfStatus)
    {
        log.debug("Entry: PCICSDao.deleteFromDeliveryInfoWithStatusDeleted()");

        Map<String, Object> parameters = new HashMap<>();
        parameters.put(STATUS_ID, pdfStatus.getStatusId());

        String errorMessage = String.format(
                "Error occurred while executing deleteFromDeliveryInfoWithStatusDeleted, Query = [%s], Params = [%s]",
                archivalDeliveryInfoDeleteQuery,
                Arrays.toString(parameters.entrySet().toArray()));

        int count = executeUpdateQuery(archivalDeliveryInfoDeleteQuery, parameters, null, errorMessage);

        log.info("Deleted {} entries from delivery_info table successfully", count);
        log.debug("Exit: PCICSDao.deleteFromDeliveryInfoWithStatusDeleted()");
    }

    public void updatePCICSInfoStatus(String streamingId, PdfStatus status)
    {
        log.debug("Entry: PCICSDao.updatePCICSInfoStatus()");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(STREAMING_ID, streamingId);
        parameters.put(STATUS_ID, status.getStatusId());

        String errorMessage = String.format(
                "Error occurred while executing updatePCICSInfoStatus, Query = [%s], Params = [%s]",
                archivalUpdatePdfStatus,
                Arrays.toString(parameters.entrySet().toArray()));
        executeUpdateQuery(archivalUpdatePdfStatus, parameters, streamingId, errorMessage);

        log.debug("Exit: PCICSDao.updatePCICSInfoStatus()");
    }

    public boolean isNewContentToggleOn()
    {
        log.debug("Entry: PCICSDao.isNewContentToggleOn()");
        Map<String, Object> parameters = new HashMap<>();
        parameters.put(PCICS_TOGGLE, "PCICS_Content_Toggle");
        parameters.put(CURRENT_DATE, String.valueOf(LocalDate.now()));
        try
        {
            List<Object> result = npJdbcTemplate.queryForList(pcicsNewContentToggleDateQuery, parameters, Object.class);
            return !result.isEmpty();
        }
        catch (DataAccessException e)
        {
            throw new SqlExecException(ErrorCode.COREDB_QUERY_EXEC_FAILURE,
                                       String.format(
                                               "PCICSDao.isNewContentToggleOn() : Error occurred while executing query to retrieve toggle date from pdfgen.pcics_content_delivery, Query = [%s], Params = [%s]",
                                               pcicsNewContentToggleDateQuery,
                                               Arrays.toString(parameters.entrySet().toArray())),
                                       null,
                                       e);
        }
    }
}