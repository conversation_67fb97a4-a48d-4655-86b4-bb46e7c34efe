package net.plus.pdfgen.util;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.boot.logging.DeferredLog;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.SystemEnvironmentPropertySource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Stream;

import static java.util.stream.Collectors.joining;
import static java.util.stream.Collectors.toMap;

/**
 * Adding first class support for Swarm and Kubernetes Secrets in Spring Boot configuration
 * and property source hierarchy. This way any Spring Boot applications can read properties
 * from Docker Swarm/Kubernetes secrets.
 *
 * <h3>Implementation Details</h3>
 * Currently we only support Swarm style secrets file. By default when Swarm manager exposes a
 * secret to a service, it would be mounted under {@code /run/secrets/{secret_name}} file. With
 * this arrangement, each file in the {@code /run/secrets} directory would be the property name
 * and its content would be the property value.
 *
 * <a href="https://docs.docker.com/engine/swarm/secrets/">Swarm Secrets</a>
 *
 * <AUTHOR> Dehghani
 * @see EnvironmentPostProcessor
 * @see SystemEnvironmentPropertySource
 */
@Component
public class ContainerSecretsAwareEnvironmentPostProcessor
        implements EnvironmentPostProcessor, ApplicationListener<ApplicationEvent>
{

    /**
     * The logger.
     */
    private static final DeferredLog log = new DeferredLog();

    /**
     * This property allows to customize the default {@code /run/secrets/} directory for
     * container property lookups.
     */
    private static final String SECRETS_DIR_PROPERTY = "container.secrets-dir";

    /**
     * The default directory inside the container to lookup for secrets that pushed by
     * the container scheduler.
     */
    private static final String DEFAULT_SECRET_DIR = "/run/secrets/";

    /**
     * Scans the secrets directory and for each file found there:
     * <pre>
     *     for each_file in secrets_dir:
     *         addProperty(filename, fileContent)
     * </pre>
     *
     * @param environment The Spring environment to customize
     * @param application The Spring Boot bootstrapper
     */
    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application)
    {
        String secretsDir = environment.getProperty(SECRETS_DIR_PROPERTY);
        if (secretsDir == null || secretsDir.trim().isEmpty())
        {
            secretsDir = DEFAULT_SECRET_DIR;
        }

        log.info(String.format("About to read container secrets from %s", secretsDir));
        Map<String, Object> readProperties = readFromSharedSecrets(secretsDir);

        if (!readProperties.isEmpty())
        {
            log.info(String.format("Adding %s secrets from container scheduler", readProperties.size()));

            SystemEnvironmentPropertySource propertySource =
                    new SystemEnvironmentPropertySource("container-secrets", readProperties);

            environment.getPropertySources().addFirst(propertySource);
        }
    }

    /**
     * Scans the {@code secretsDir} directory and for each file, add a property named after the
     * file paired with its content as the property value.
     *
     * @param secretsDir Represents the secrets directory
     * @return Map of property names and property values.
     */
    private Map<String, Object> readFromSharedSecrets(String secretsDir)
    {
        Path directory;
        try
        {
            directory = Paths.get(secretsDir);
        }
        catch (Exception e)
        {
            log.warn(String.format("The provided container secrets directory was invalid: %s", secretsDir), e);

            return Collections.emptyMap();
        }

        if (!directory.toFile().exists())
        {
            log.warn(String.format("The provided container secrets directory not found: %s", secretsDir));
            return Collections.emptyMap();
        }

        try (Stream<Path> paths = Files.list(directory))
        {
            return paths.filter(this::isFile)
                        .collect(toMap(this::filename, this::content));
        }
        catch (Exception e)
        {
            log.warn("Failed to read secrets from secrets directory", e);
            return Collections.emptyMap();
        }
    }

    /**
     * @param path The path to inspect
     * @return Is the given {@code path} represents a file?
     */
    private boolean isFile(Path path)
    {
        return path.toFile().isFile();
    }

    /**
     * @param path The path to extract its filename
     * @return The filename
     */
    private String filename(Path path)
    {
        return path.getFileName().toString();
    }

    /**
     * @param path Represents a path to a file
     * @return The corresponding file content
     */
    private String content(Path path)
    {
        try (Stream<String> content = Files.lines(path))
        {
            return content.filter(line -> !line.trim().isEmpty()).collect(joining());
        }
        catch (IOException e)
        {
            log.warn("Failed to read the secret file", e);
            return "";
        }
    }

    @Override
    public void onApplicationEvent(ApplicationEvent event)
    {
        log.replayTo(ContainerSecretsAwareEnvironmentPostProcessor.class);
    }
}
