package net.plus.pdfgen.scheduler;

import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import net.plus.pdfgen.constant.PdfStatus;
import net.plus.pdfgen.dao.PCICSDao;
import net.plus.pdfgen.util.FileUtil;

@Component
@Slf4j
@AllArgsConstructor
public class PcicsCleanupTask
{
    private final PCICSDao pcicsDao;
    private final FileUtil fileUtil;

    public void run()
    {
        Optional<Map<String, String>> eligiblePdfs = pcicsDao.retrieveArchivalEligiblePdfs();

        if (eligiblePdfs.isPresent() && !eligiblePdfs.get().isEmpty())
        {
            log.info("StreamingId of Pdfs considered for Archival cleanup are : [ {} ]",
                     eligiblePdfs.get().keySet().stream().map(str -> String.format("'%s'", str)).collect(
                             Collectors.joining(",")));
            eligiblePdfs.get().forEach(this::deletePdfFromSharedLocation);
        }
        else
        {
            log.info("No eligible Pdfs were found in PCICS_INFO to perform Archival cleanup task.");
        }
        deletePdfFromDb();
    }

    private void deletePdfFromSharedLocation(String streamingId, String pdfName)
    {
        if (pdfName == null || fileUtil.validateAndDeleteFile(streamingId, pdfName))
        {
            pcicsDao.updatePCICSInfoStatus(streamingId, PdfStatus.DELETED);
        }
    }

    private void deletePdfFromDb()
    {
        pcicsDao.deleteFromDeliveryInfo(PdfStatus.DELETED);
        pcicsDao.deleteFromPCICSInfo(PdfStatus.DELETED);
    }
}
