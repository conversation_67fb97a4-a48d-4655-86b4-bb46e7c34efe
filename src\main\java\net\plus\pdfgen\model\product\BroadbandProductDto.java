package net.plus.pdfgen.model.product;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.time.LocalDate;
import java.time.ZoneId;



@ToString(callSuper = true)
@Getter
@Setter
@NoArgsConstructor
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(namespace = "http://www.plus.net/pcicsrequest")
public class BroadbandProductDto extends ContractedProductDto
{
    private static final Logger log = LoggerFactory.getLogger(BroadbandProductDto.class);

    private String estimatedDownloadSpeedMin;
    private String estimatedDownloadSpeedMax;
    private String estimatedUploadSpeedMin;
    private String estimatedUploadSpeedMax;
    private String minGuaranteedSpeed;
    private String technologyType;
    private String displayName;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate  futureDate1;
    private BigDecimal futurePrice1;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate  futureDate2;
    private BigDecimal futurePrice2;



    public void setTechnologyType(String technologyType) {
        this.technologyType = technologyType;
        if (technologyType != null) {
            switch (technologyType) {
                case "TECHNOLOGY_TYPE_COPPER":
                    this.displayName = "Copper";
                    break;
                case "TECHNOLOGY_TYPE_PART_FIBRE":
                    this.displayName = "Part Fibre";
                    break;
                case "TECHNOLOGY_TYPE_FULL_FIBRE":
                    this.displayName = "Full Fibre";
                    break;
                default:
                    this.displayName = null;
                    log.error("The TechnologyType passed in BroadbandProductDto is: [{}]", technologyType);
            }
        } else {
            this.displayName = null;
            log.error("The TechnologyType passed in BroadbandProductDto is null");
        }
    }

    public Date getFutureDate1AsDate() {
        return Date.from(futureDate1.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    public Date getFutureDate2AsDate() {
        return Date.from(futureDate2.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

}
