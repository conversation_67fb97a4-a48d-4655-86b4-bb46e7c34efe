package net.plus.pdfgen.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

import lombok.*;

import java.io.Serializable;

@Getter
@Setter
@Builder
@ToString
@XmlRootElement(name = "pcicsresponse",
                namespace = "http://www.plus.net/pcicsresponse")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(namespace = "http://www.plus.net/pcicsresponse")
public class PCICSResponseDto implements Serializable
{
    private static final long serialVersionUID = 1L;

    private String orderId;
    private Integer accountId;
    private String streamingId;
    private String streamingUrl;
    private String errorMessage;
}