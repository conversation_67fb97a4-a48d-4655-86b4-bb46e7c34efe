package net.plus.pdfgen.model;

import javax.validation.constraints.NotNull;
import javax.xml.bind.annotation.Facets;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import net.plus.pdfgen.exception.error.ErrorCode;

@ToString
@Getter
@Setter
@NoArgsConstructor
@XmlRootElement(name = "deliverycomms",
                namespace = "http://www.plus.net/deliverycomms")
@XmlType(namespace = "http://www.plus.net/deliverycomms")
@XmlAccessorType(XmlAccessType.FIELD)
public class DeliveryCommsDto
{

    private String orderId;

    private String handle;

    @XmlElement(required = true)
    @Facets(enumeration = { "Email" })
    @NotNull(message = ErrorCode.MISSING_DELIVERYTYPE)
    private String deliveryType;

    @XmlElement(required = true)
    @Facets(enumeration = { "SUCCESS", "FAILURE" })
    @NotNull(message = ErrorCode.MISSING_DELIVERY_COMMS)
    private String status;

}