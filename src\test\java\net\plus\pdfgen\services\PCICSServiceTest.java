package net.plus.pdfgen.services;

import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;

import net.plus.pdfgen.constant.PdfStatus;
import net.plus.pdfgen.dao.PCICSDao;
import net.plus.pdfgen.exception.PdfOperationException;
import net.plus.pdfgen.exception.StreamingIdNotFoundExeception;
import net.plus.pdfgen.model.DeliveryCommsDto;
import net.plus.pdfgen.model.PCICSRequestDto;
import net.plus.pdfgen.model.PCICSResponseDto;
import net.plus.pdfgen.model.PCICSStatusDto;
import net.plus.pdfgen.service.DeliveryCommsService;
import net.plus.pdfgen.service.PCICSService;
import net.plus.pdfgen.util.FileUtil;
import net.plus.pdfgen.util.HtmlUtil;
import net.plus.pdfgen.util.PdfUtil;
import net.plus.pdfgen.util.Stream;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Optional;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class PCICSServiceTest {

    private String streamingUrl = "https://plus.net/pci-terms?streaming-id";

    String streamingId = "0a49af44fb126c85d18e54a7a3fbd959";

    @Mock
    private PCICSDao pcicsDao;

    @Mock
    private HtmlUtil htmlUtil;

    @Mock
    private PdfUtil pdfUtil;

    @Mock
    private Stream stream;

    PCICSResponseDto pcicsResponseDto;

    PCICSRequestDto pcicsRequestDto;

    private PCICSService pcicsService;

    MockedStatic<Stream> streamMockedStatic;

    @Before
    public void setUp()
    {
        pcicsService = new PCICSService(htmlUtil, pdfUtil, stream, pcicsDao);
        streamMockedStatic = Mockito.mockStatic(Stream.class);
        mockSampleRequest();
    }

    @After
    public void destroy()
    {
        streamMockedStatic.close();
        mockSampleRequest();
    }

    @Test
    public void shouldGeneratePCICS() throws Exception
    {
        mockSampleResponse();
        when(Stream.getId()).thenReturn(streamingId);
        when(stream.getUrl(streamingId)).thenReturn(streamingUrl);
        PCICSResponseDto response = pcicsService.generatePCICS(pcicsRequestDto);
        Assert.assertNotNull(response);
        Assert.assertEquals(response.getAccountId(), pcicsResponseDto.getAccountId());
        Assert.assertEquals(response.getOrderId(), pcicsResponseDto.getOrderId());
        Assert.assertEquals(response.getStreamingId(), pcicsResponseDto.getStreamingId());
        Assert.assertEquals(response.getStreamingUrl(), pcicsResponseDto.getStreamingUrl());

    }

    @Test(expected = PdfOperationException.class)
    public void shouldThrowExceptionWhenHtmlIsParsed() {
        mockSampleResponse();
        when(Stream.getId()).thenReturn(streamingId);
        doThrow(RuntimeException.class).when(htmlUtil).parseHtmlTemplate(pcicsRequestDto);
        verifyNoInteractions(pdfUtil, stream);
        verify(pcicsDao, never()).insertOrUpdatePcicsInfoRecord(pcicsRequestDto,
                                                                streamingId,
                                                                null,
                                                                PdfStatus.PDFGENFAILED.getStatusId());
        verifyNoMoreInteractions(pcicsDao);
        pcicsService.generatePCICS(pcicsRequestDto);
    }

    @Test
    public void shouldRetrievePDF() throws IOException
    {
        byte[] expectedResponse = "<html>Sample</html>".getBytes(StandardCharsets.UTF_8);
        String filePath = "./src/test/resources/";
        doReturn(filePath).when(pcicsDao).retrieveFilePath(streamingId);
        doReturn(expectedResponse).when(pdfUtil).convertPDFtoByteStream(filePath);
        byte[] actualResponse = pcicsService.retrievePdf(streamingId);
        Assert.assertNotNull(actualResponse);
        Assert.assertEquals(actualResponse, expectedResponse);
    }

    @Test(expected = PdfOperationException.class)
    public void shouldThrowExceptionWhileConvertingPdf() throws IOException
    {
        String filePath = "./src/test/resources/";
        doReturn(filePath).when(pcicsDao).retrieveFilePath(streamingId);
        doThrow(IOException.class).when(pdfUtil).convertPDFtoByteStream(filePath);
        pcicsService.retrievePdf(streamingId);
    }

    @Test(expected = PdfOperationException.class)
    public void shouldThrowExceptionWithInvalidFilePath() throws IOException
    {
        doReturn(null).when(pcicsDao).retrieveFilePath(streamingId);
        verifyNoInteractions(pdfUtil);
        pcicsService.retrievePdf(streamingId);
    }

    @Test
    public void shouldUpdateJourneyCompletionStatus(){
        PCICSStatusDto statusDTO = new PCICSStatusDto();
        statusDTO.setOrderId("1234");
        statusDTO.setAccountId(1212121);
        doReturn(true).when(pcicsDao).isStreamingIdExist(streamingId);
        pcicsService.updateJourneyCompletionStatus(streamingId, statusDTO);
        verify(pcicsDao, times(1)).updatePCICSInfoRecord(streamingId, statusDTO);
    }

    @Test(expected = StreamingIdNotFoundExeception.class)
    public void shouldThrowExceptionWhenStreamingIdNotFound(){
        doReturn(false).when(pcicsDao).isStreamingIdExist(streamingId);
        pcicsService.updateJourneyCompletionStatus(streamingId, any(PCICSStatusDto.class));
        verify(pcicsDao, never()).updatePCICSInfoRecord(streamingId, any(PCICSStatusDto.class));
    }

    private void mockSampleRequest(){
        String createPDFRequest = getTheParseInputRequest();
        Gson gson = new Gson();
        pcicsRequestDto = gson.fromJson(createPDFRequest, PCICSRequestDto.class);
    }

    private String getTheParseInputRequest() {
        final String jsonRequestFileName = "./src/test/resources/input/CreatePdfRequest.json";
        Optional<String> parsedRequest = Optional.empty();
        try {
            parsedRequest = Optional.of(new String(Files.readAllBytes(Paths.get(jsonRequestFileName))));
        } catch (IOException e) {
            log.error("Reading the Json Request from the file is failed");
        }
        return parsedRequest.orElse(null);
    }

    private void mockSampleResponse()
    {
        pcicsResponseDto = PCICSResponseDto.builder()
                                           .streamingUrl(streamingUrl)
                                           .streamingId(streamingId)
                                           .accountId(123456)
                                           .orderId("123456")
                                           .build();
    }

}
