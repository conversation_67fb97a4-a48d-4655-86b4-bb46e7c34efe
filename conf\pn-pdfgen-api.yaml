version: '3.6'

services:
  pdfgen-api:
    image: ${DOCKER_REGISTRY}/plusnet/pn-pdfgen-api:${version}
    volumes:
      - type: bind
        source: /etc/pki/java/store/pdfgen
        target: /keystore
        read_only: true
      - type: bind
        source: /share/pdfgen/
        target: /share/pdfgen/
        read_only: false
    networks:
      - pn-pdfgen-api
    ports:
      - 10022:8089
    env_file:
      - pn-pdfgen-api.env
    secrets:
      - PDFGEN_TRUSTSTORE_PASS
      - PDFGEN_COREDB_PASS
      - PDFGEN_APPDB_PASS
    deploy:
      mode: global
      placement:
        constraints:
          - node.labels.middleware == true
      resources:
        limits:
          cpus: '1.5'
          memory: 3G
      update_config:
        parallelism: 1
        delay: 30s
      restart_policy:
        condition: on-failure
        max_attempts: 3
        window: 120s
        delay: 5s

networks:
  pn-pdfgen-api:
    driver: overlay
    driver_opts:
      encrypted: "true"
    attachable: true

secrets:
  PDFGEN_TRUSTSTORE_PASS:
    external: true
    name: PDFGEN_TRUSTSTORE_PASS
  PDFGEN_COREDB_PASS:
    external: true
    name: PDFGEN_COREDB_PASS
  PDFGEN_APPDB_PASS:
    external: true
    name: PDFGEN_APPDB_PASS

