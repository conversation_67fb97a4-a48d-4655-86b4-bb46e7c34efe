package net.plus.pdfgen.model;

import java.math.BigDecimal;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import net.plus.pdfgen.deserializer.CurrencyDeserializer;
import net.plus.pdfgen.exception.error.ErrorCode;

@ToString
@Getter
@Setter
@NoArgsConstructor
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(namespace = "http://www.plus.net/pcicsrequest")
public class OneOffChargeDto
{
    @XmlElement(required = true)
    @NotEmpty(message = ErrorCode.MISSING_OOCNAME)
    private String oocName;

    @XmlElement(required = true)
    @NotNull(message = ErrorCode.MISSING_OOCPRICE)
    @JsonDeserialize(using = CurrencyDeserializer.class)
    private BigDecimal oocPrice;

}