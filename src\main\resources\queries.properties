#Coredb queries
pcics.info.status.update.query=UPDATE pdfgen.pcics_info SET account_id=:account_id,order_id=:order_id, status_id=:status_id where streaming_id=:streaming_id
pcics.info.select.filePath.query=SELECT file_path FROM pdfgen.pcics_info WHERE streaming_id=:streaming_id
pcics.info.streamingid.exists.query=SELECT count(*) FROM pdfgen.pcics_info WHERE streaming_id=:streaming_id
delivery.info.comms.insert.or.update.query=INSERT INTO pdfgen.delivery_info(streaming_id, order_id, delivery_type, handle, created_dtm, status) VALUES (:streaming_id, :order_id, :delivery_type, :handle, CURRENT_TIMESTAMP, :status) ON DUPLICATE KEY UPDATE status=:status, order_id=:order_id, handle=:handle
pcics.info.insert.or.update.query=INSERT INTO pdfgen.pcics_info(streaming_id, account_id, order_id, customer_type, file_path, created_dtm, status_id) VALUES (:streaming_id, :account_id, :order_id, :customer_type, :file_path, CURRENT_TIMESTAMP, :status_id) ON DUPLICATE KEY UPDATE status_id=:status_id

pcics.info.update.status.query=UPDATE pdfgen.pcics_info SET status_id=:status_id where streaming_id=:streaming_id
pcics.info.delete.pdfs.query=DELETE FROM pdfgen.pcics_info WHERE status_id=:status_id
pcics.info.archival.eligible.pdfs.query=SELECT pi.streaming_id, pi.file_path FROM pdfgen.pcics_info pi INNER JOIN pdfgen.pdfgenstatus ps ON pi.status_id = ps.status_id WHERE DATE(pi.created_dtm) <= DATE(NOW() - INTERVAL 14 DAY) AND ps.status IN ('PDFGENFAILED', 'INITIATED') UNION SELECT pi.streaming_id, pi.file_path FROM pdfgen.pcics_info pi INNER JOIN pdfgen.pdfgenstatus ps ON pi.status_id=ps.status_id WHERE DATE(pi.created_dtm) <= DATE(NOW() - INTERVAL 6 YEAR) AND ps.status = 'COMPLETED'
delivery.info.archival.delete.entries.query=DELETE FROM pdfgen.delivery_info WHERE streaming_id IN (SELECT streaming_id FROM pdfgen.pcics_info WHERE status_id=:status_id)
pcics.select.contenttoggledate.query=SELECT onDateTime FROM dbAdminTools.tblFeatureToggle where toggleName = :pcics_toggle and onDateTime <= :current_date

