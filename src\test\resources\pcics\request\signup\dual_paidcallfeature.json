{"journeyContext": "SignUp", "customerType": "Residential", "orderId": "629ad7bc-4e55-40e5-b9d6-5e07459786df", "taxTreatment": "IncVAT", "broadbandProduct": {"c2mProductName": "FibreSignupDualPaidCallfeature", "productDisplayName": "Fibre Signup Dual Paid Callfeature", "discountedPrice": 22.28, "fullPrice": 25.28, "contractDuration": 18, "estimatedDownloadSpeedMin": "50Mb", "estimatedDownloadSpeedMax": "80Mb", "estimatedUploadSpeedMin": "50Mb", "estimatedUploadSpeedMax": "80Mb", "minGuaranteedSpeed": "40Mb", "technologyType": "TECHNOLOGY_TYPE_FULL_FIBRE"}, "lineRentalProduct": {"c2mProductName": "LineRental", "productDisplayName": "Line Rental", "discountedPrice": 28.99, "fullPrice": 32.99, "contractDuration": 18}, "callPlanProduct": {"c2mProductName": "EveningAndWeekendUKAndMobileCalls", "productDisplayName": "Evening & Weekend UK & Mobile", "fullPrice": 12}, "callFeatures": [{"c2mProductName": "VoicemailExtra", "productDisplayName": "Voicemail extra", "productDescription": "Premium voicemail with lots of features.", "fullPrice": 2}, {"c2mProductName": "AnonymousCallReject", "productDisplayName": "Anonymous Call Reject", "productDescription": "You can use this to block calls from withheld numbers.", "fullPrice": 2}], "addOns": [{"c2mProductName": "PlusnetProtect", "productDisplayName": "Plusnet Protect", "fullPrice": 0.0}, {"c2mProductName": "BTSportApp", "productDisplayName": "BT Sport App", "fullPrice": 6}, {"c2mProductName": "MobileBoltOn", "productDisplayName": "Calls to mobiles", "fullPrice": 3}], "oneOffCharges": [{"oocName": "Installation Charge", "oocPrice": 8.99}, {"oocName": "Postage and Packaging", "oocPrice": 6.99}], "equipmentList": ["Plusnet Hub 2", "Plusnet Hub Zero"], "totalOocPrice": 5, "totalCallFeatureBundlePrice": 5, "totalMonthlyCost": 65}