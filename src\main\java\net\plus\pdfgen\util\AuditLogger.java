package net.plus.pdfgen.util;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;

import java.io.IOException;

@Component
@Slf4j
public class AuditLogger implements Filter
{
    private static final Logger AUDIT_LOGGER = LoggerFactory.getLogger("AuditLogger");
    private static final String API_CONTEXT = "/pdfgen/";

    @Override
    public void init(FilterConfig filterConfig)
    {
        log.debug("AuditLogger.init()");
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException
    {
        log.debug("Entry: doFilter()");

        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        final String url = getRequestedUrl(httpServletRequest);
        final long beginTimestamp = System.currentTimeMillis();
        final boolean isPdfgenApiUrl = httpServletRequest.getRequestURI().startsWith(API_CONTEXT);
        final String sessionId = extractSessionIdFromRequest(httpServletRequest);
        final String correlationId = extractCorrelationIdFromRequest(httpServletRequest);
        final String sourceIp = httpServletRequest.getRemoteHost();

        MDC.put("audit_metadata",
                String.format("[%s]", getAuditMetadata(beginTimestamp, sessionId, correlationId, sourceIp)));

        AUDIT_LOGGER.info("Starting [{}] {}", httpServletRequest.getMethod(), url);

        chain.doFilter(request, response);

        if (isPdfgenApiUrl)
        {
            AUDIT_LOGGER.info("Ending [{}] {} in {} ms", httpServletRequest.getMethod(), url,
                              (System.currentTimeMillis() - beginTimestamp));
        }

        log.debug("Exit: doFilter()");
    }

    private String getAuditMetadata(Long beginTimestamp, String sessionId, String correlationId, String sourceIp)
    {
        final StringBuilder metadata = new StringBuilder();
        if (StringUtils.isNotBlank(correlationId))
        {
            metadata.append(String.format("previousCorrelationId=%s", correlationId));
        }
        if (StringUtils.isNotBlank(sessionId))
        {
            metadata.append(String.format(" sessionId=%s", sessionId));
        }
        if (StringUtils.isNotBlank(sourceIp))
        {
            metadata.append(String.format(" sourceIp=%s", sourceIp));
        }
        metadata.append(String.format(" timestamp=%s", beginTimestamp));
        return metadata.toString();
    }

    private String getRequestedUrl(HttpServletRequest httpServletRequest)
    {
        final String requestPath = httpServletRequest.getRequestURL().toString();
        final String queryString = httpServletRequest.getQueryString();
        final StringBuilder urlBuilder = new StringBuilder(requestPath);

        if (StringUtils.isNotBlank(queryString))
        {
            urlBuilder.append("?").append(queryString);
        }

        return urlBuilder.toString();
    }

    private String extractCorrelationIdFromRequest(HttpServletRequest httpServletRequest)
    {
        return httpServletRequest.getHeader("X-Correlation-ID");
    }

    private String extractSessionIdFromRequest(final HttpServletRequest httpServletRequest)
    {
        return httpServletRequest.getHeader("X-Session-ID");
    }

    @Override
    public void destroy()
    {
        log.debug("AuditLogger.destroy()");
    }

}
