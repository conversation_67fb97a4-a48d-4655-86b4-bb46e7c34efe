{"journeyContext": "Recontract", "customerType": "Residential", "orderId": "629ad7bc-4e55-40e5-b9d6-5e07459786df", "accountId": 123456, "taxTreatment": "IncVAT", "broadbandProduct": {"c2mProductName": "UnlimitedFibreRecontractDual", "productDisplayName": "Unlimited Fibre Recontract Dual", "discountedPrice": 22.28, "fullPrice": 25.28, "contractDuration": 18, "estimatedDownloadSpeedMin": "50Mb", "estimatedDownloadSpeedMax": "80Mb", "estimatedUploadSpeedMin": "50Mb", "estimatedUploadSpeedMax": "80Mb", "minGuaranteedSpeed": "40Mb"}, "lineRentalProduct": {"c2mProductName": "LineRental", "productDisplayName": "Line Rental", "discountedPrice": 28.99, "fullPrice": 32.99, "contractDuration": 18}, "callPlanProduct": {"c2mProductName": "EveningAndWeekendUKAndMobileCalls", "productDisplayName": "Evening and Weekend UK and Mobile Calls", "fullPrice": 12}, "callFeatures": [{"c2mProductName": "VoicemailExtra", "productDisplayName": "Voicemail Extra (<PERSON> Minder)", "productDescription": "Premium voicemail with lots of features.", "fullPrice": 2}, {"c2mProductName": "AnonymousCallReject", "productDisplayName": "Anonymous Call Reject", "productDescription": "You can use this to block calls from withheld numbers.", "fullPrice": 2}, {"c2mProductName": "ReminderCall", "productDisplayName": "Reminder Call", "productDescription": "Turn your house phone into an alarm clock by setting a reminder.", "fullPrice": 0}, {"c2mProductName": "CallDivert", "productDisplayName": "Call Divert", "productDescription": "Divert your calls to your UK landline, mobile and most international destinations.", "fullPrice": 0}], "addOns": [{"c2mProductName": "BTSportApp", "productDisplayName": "BT Sport app", "fullPrice": 6}, {"c2mProductName": "CallplanBolton", "productDisplayName": "Call plan Bolton", "fullPrice": 3}], "oneOffCharges": [{"oocName": "Installation Charge", "oocPrice": 8.99}, {"oocName": "Postage and Packaging", "oocPrice": 6.99}], "equipmentList": ["Plusnet Hub 2", "Plusnet Hub Zero"], "totalOocPrice": 5, "totalCallFeatureBundlePrice": 5, "totalMonthlyCost": 65}