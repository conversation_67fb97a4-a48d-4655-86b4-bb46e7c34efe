package net.plus.pdfgen.service;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import net.plus.pdfgen.constant.PdfStatus;
import net.plus.pdfgen.dao.PCICSDao;
import net.plus.pdfgen.exception.PdfOperationException;
import net.plus.pdfgen.exception.StreamingIdNotFoundExeception;
import net.plus.pdfgen.exception.error.ErrorCode;
import net.plus.pdfgen.model.PCICSRequestDto;
import net.plus.pdfgen.model.PCICSResponseDto;
import net.plus.pdfgen.model.PCICSStatusDto;
import net.plus.pdfgen.util.HtmlUtil;
import net.plus.pdfgen.util.Stream;
import net.plus.pdfgen.util.PdfUtil;

import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.InvalidPathException;
import java.util.Optional;

@Service
@Slf4j
@AllArgsConstructor
public class PCICSService
{

    private final HtmlUtil htmlUtil;
    private final PdfUtil pdfUtil;
    private final Stream stream;
    private final PCICSDao pcicsDao;

    public PCICSResponseDto generatePCICS(final PCICSRequestDto pcicsRequest)
    {
        log.debug("Entry: PCICSService.generatePCICS()");
        String filePath = null;
        PCICSResponseDto pciResponse = PCICSResponseDto.builder().streamingId(Stream.getId())
                                                       .accountId(pcicsRequest.getAccountId())
                                                       .orderId(pcicsRequest.getOrderId()).build();

        String streamingId = pciResponse.getStreamingId();

        if(!Optional.ofNullable(pcicsRequest.getCallFeatureList()).isEmpty()) {
            validateCallfeatureDescription(pcicsRequest, streamingId);
        }

        try
        {
            pcicsRequest.setNewContentToggleOn(pcicsDao.isNewContentToggleOn());
            log.info("pcicsRequest.isNewContentToggleOn : {} ", pcicsRequest.isNewContentToggleOn());
            String htmlTemplate = htmlUtil.parseHtmlTemplate(pcicsRequest);
            log.trace("Html Template : {} ", htmlTemplate);

            filePath = pdfUtil.generatePdfFromHtml(htmlTemplate, streamingId, pcicsRequest);
            pcicsDao.insertOrUpdatePcicsInfoRecord(pcicsRequest,
                                                   streamingId,
                                                   filePath,
                                                   PdfStatus.INITIATED.getStatusId());
            pciResponse.setStreamingUrl(stream.getUrl(streamingId));
        }
        catch (Exception e)
        {
            pcicsDao.insertOrUpdatePcicsInfoRecord(pcicsRequest,
                                                   streamingId,
                                                   filePath,
                                                   PdfStatus.PDFGENFAILED.getStatusId());
            throw new PdfOperationException(ErrorCode.CREATE_PDF_FAILURE,
                                            "generatePCICS: Unknown exception encountered during PDF creation",
                                            streamingId, e);
        }

        log.debug("Exit: PCICSService.generatePCICS()");
        return pciResponse;
    }

    private void validateCallfeatureDescription(PCICSRequestDto pcicsRequest, String streamingId)
    {
        pcicsRequest.getCallFeatureList().forEach(callFeatureDto ->
                                                  {
                                                      if (Optional.ofNullable(callFeatureDto.getProductDescription()).isEmpty() || callFeatureDto.getProductDescription().isBlank())
                                                      {
                                                          if (Optional.ofNullable(callFeatureDto.getProductDisplayName()).isPresent() )
                                                          {
                                                              log.warn("Missing Call feature description for callFeature: '{}' and streamingId: '{}' ",
                                                                       callFeatureDto.getProductDisplayName(), streamingId);
                                                          }
                                                      }
                                                  });
    }

    public byte[] retrievePdf(final String streamingId)
    {
        log.debug("Entry: PCICSService.retrievePdf()");
        byte[] pdfContent;

        String filePath = pcicsDao.retrieveFilePath(streamingId);
        if (null != filePath)
        {
            try
            {
                pdfContent = pdfUtil.convertPDFtoByteStream(filePath);
            }
            catch (IOException |
                    InvalidPathException | SecurityException e)
            {
                throw new PdfOperationException(ErrorCode.RETRIEVE_PDF_FAILURE,
                                                "Encountered failure during retrieving PDF from shared location",
                                                streamingId,
                                                e);
            }
        }
        else
        {
            throw new PdfOperationException(ErrorCode.RETRIEVE_PDF_FAILURE,
                                            "Encountered failure during retrieving PDF from shared location",
                                            streamingId);
        }

        log.debug("Exit: PCICSService.retrievePdf()");

        return pdfContent;
    }

    public void updateJourneyCompletionStatus(final String streamingId, final PCICSStatusDto statusDto)
    {
        log.debug("Entry: PCICSService.updateJourneyCompletionStatus()");

        if (pcicsDao.isStreamingIdExist(streamingId))
        {
            pcicsDao.updatePCICSInfoRecord(streamingId, statusDto);
        }
        else
        {
            throw new StreamingIdNotFoundExeception(ErrorCode.STREAMING_ID_NOT_FOUND,
                                                    "updateJourneyCompletionStatus : Unable to find input streamingId in pcics_info",
                                                    streamingId);
        }

        log.debug("Exit: PCICSService.updateJourneyCompletionStatus()");
    }

}