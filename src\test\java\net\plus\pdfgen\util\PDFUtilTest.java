package net.plus.pdfgen.util;

import com.google.gson.Gson;

import lombok.extern.slf4j.Slf4j;

import net.plus.pdfgen.dao.PCICSDao;
import net.plus.pdfgen.exception.PdfOperationException;
import net.plus.pdfgen.model.PCICSRequestDto;
import org.junit.Assert;
import org.junit.Before;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.core.classloader.annotations.PowerMockIgnore;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Optional;

import static org.mockito.Mockito.doReturn;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class PDFUtilTest {

    final String streamingId = "0a49af44fb126c85d18e54a7a3fbd959";

    @Mock
    private FileUtil fileUtil;

    @Mock
    private PCICSDao pcicsDao;

    PdfUtil pdfUtil;

    private PCICSRequestDto pcicsRequestDto;

    @Before
    public void setUp() {
        pdfUtil = new PdfUtil(fileUtil, pcicsDao);
        mockSampleRequest();
    }

    @Test
    public void shouldGeneratePdfFromHtml(){
        String expectedFilePath = "./src/test/resources/sample.pdf";
        doReturn(expectedFilePath).when(fileUtil).getFilePath(streamingId, pcicsRequestDto);
        String actualFilePath = pdfUtil.generatePdfFromHtml("<html>Sample</html>", streamingId, pcicsRequestDto);
        Assert.assertEquals(expectedFilePath, actualFilePath);
    }

    @Test(expected = PdfOperationException.class)
    public void shouldThrowExceptionWhenFilePathIsNull(){
        pdfUtil.generatePdfFromHtml("<html>Sample</html>", streamingId, pcicsRequestDto);
    }

    @Test
    public void shouldTransformPDFtoByteStream() throws IOException {
        String filePath = "./src/test/resources/sample.pdf";
        byte[] response = pdfUtil.convertPDFtoByteStream(filePath);
        Assert.assertNotNull(response);
        Assert.assertTrue(new String(response).contains("PDF"));
    }

    @Test(expected = IOException.class)
    public void shouldThrowExceptionWhenPdfFileNotExists() throws IOException {
        String filePath = "./src/test/resources/nofile.pdf";
        pdfUtil.convertPDFtoByteStream(filePath);
    }

    private void mockSampleRequest(){
        String createPDFRequest = getTheParseInputRequest();
        Gson gson = new Gson();
        pcicsRequestDto = gson.fromJson(createPDFRequest, PCICSRequestDto.class);
    }

    private String getTheParseInputRequest() {
        final String jsonRequestFileName = "./src/test/resources/input/CreatePdfRequest.json";
        Optional<String> parsedRequest = Optional.empty();
        try {
            parsedRequest = Optional.of(new String(Files.readAllBytes(Paths.get(jsonRequestFileName))));
        } catch (IOException e) {
            log.error("Reading the json request from the file failed.");
        }
        return parsedRequest.orElse(null);
    }
}
