package net.plus.pdfgen.regression;

import net.plus.pdfgen.util.HtmlUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.PropertySource;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;

import lombok.extern.slf4j.Slf4j;

import net.plus.pdfgen.PcicsPdfgenApplication;

import java.time.LocalDate;

/**
 * Test class to generate PDF for various housemove journey scenarios,
 * compare it with the sample pdf files
 * and create a new file with differences incase of mismatches
 */
@Slf4j
@RunWith(SpringRunner.class)
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = PcicsPdfgenApplication.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@AutoConfigureMockMvc
@PropertySource("classpath:application-test.properties")
@ActiveProfiles("test")
public class HousemoveJourneyTest
{

    private final static String HOUSEMOVE_JOURNEY = "housemove";

    @Value(("${pcics.stream.url}"))
    private String url;

    @Value("${pcics.file.location}")
    private String fileLocation;

    @Value("${pcics.dir.location}")
    private String dirLocation;

    @Value("${pdf.dir.date.format}")
    private String dateFormat;

    @Autowired
    private HtmlUtil htmlUtil;
    private PdfComparison pdfComparison;

    @Before
    public void initialise() {
        pdfComparison = new PdfComparison(url, dirLocation, fileLocation, dateFormat);
        htmlUtil.setFixedDate(LocalDate.of(2024, 12, 10));
    }

    @Test
    public void dualHousemovePdfComparisonTest()  {
        log.debug("Begin: dualHousemovePdfComparisonTest");
        pdfComparison.initializeParameters(HOUSEMOVE_JOURNEY, "dual_full");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Dual Housemove generated pdf that doesn't match", isEqual);
        log.debug("End: dualHousemovePdfComparisonTest");
    }

    @Test
    public void housemoveWithFullFibre300Bb()
    {
        log.debug("Begin: housemoveWithFullFibre300Bb");
        pdfComparison.initializeParameters(HOUSEMOVE_JOURNEY, "fullFibre300");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("HouseMove with Full Fibre 300 BB product generated pdf that doesn't match", isEqual);
        log.debug("End: housemoveWithFullFibre300Bb");
    }

    @Test
    public void housemoveWithFullFibre500Bb()
    {
        log.debug("Begin: housemoveWithFullFibre500Bb");
        pdfComparison.initializeParameters(HOUSEMOVE_JOURNEY, "fullFibre500");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("HouseMove with Full Fibre 500 BB product generated pdf that doesn't match", isEqual);
        log.debug("End: housemoveWithFullFibre500Bb");
    }

    @Test
    public void housemoveWithFibreBb()
    {
        log.debug("Begin: housemoveWithFibreBb");
        pdfComparison.initializeParameters(HOUSEMOVE_JOURNEY, "fibre");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("HouseMove with Fibre BB product generated pdf that doesn't match", isEqual);
        log.debug("End: housemoveWithFibreBb");
    }

    @Test
    public void housemoveWithFullFibre145Bb()
    {
        log.debug("Begin: housemoveWithFullFibre145Bb");
        pdfComparison.initializeParameters(HOUSEMOVE_JOURNEY, "fullFibre145");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("HouseMove with Full Fibre 145 BB product generated pdf that doesn't match", isEqual);
        log.debug("End: housemoveWithFullFibre145Bb");
    }

    @Test
    public void housemoveWithFullFibre74Bb()
    {
        log.debug("Begin: housemoveWithFullFibre74Bb");
        pdfComparison.initializeParameters(HOUSEMOVE_JOURNEY, "fullFibre74");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("HouseMove with Full Fibre 74 BB product generated pdf that doesn't match", isEqual);
        log.debug("End: housemoveWithFullFibre74Bb");
    }

    @Test
    public void housemoveWithFullFibre900Bb()
    {
        log.debug("Begin: housemoveWithFullFibre900Bb");
        pdfComparison.initializeParameters(HOUSEMOVE_JOURNEY, "fullFibre900");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("HouseMove with Full Fibre 900 BB product generated pdf that doesn't match", isEqual);
        log.debug("End: housemoveWithFullFibre900Bb");
    }

    @Test
    public void housemoveWithUnlimitedBb()
    {
        log.debug("Begin: housemoveWithUnlimitedBb");
        pdfComparison.initializeParameters(HOUSEMOVE_JOURNEY, "unlimited");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("HouseMove with Unlimited BB product generated pdf that doesn't match", isEqual);
        log.debug("End: housemoveWithUnlimitedBb");
    }



}
