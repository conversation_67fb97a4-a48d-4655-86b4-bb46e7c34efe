<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <property name="LOG_HOME" value="./src/test/resources/log/pn-pdfgen-api"/>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>
                [%date{yyyy-MMM-dd HH:mm:ss}] [${hostname}] [pn-pdfgen-api] [ %-5level] [%c] %mdc{audit_metadata} %msg%n
            </Pattern>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/pn-pdfgen-api.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>
                [%date{yyyy-MMM-dd HH:mm:ss.SSS}] [${hostname}] [pn-pdfgen-api] [ %-5level] %mdc{audit_metadata}
                [%thread] [%c] %msg%n
            </Pattern>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- rollover daily -->
            <fileNamePattern>${LOG_HOME}/archived/pn-pdfgen-api.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>10MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
    </appender>

    <appender name="FILE-AUDIT" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/pn-pdfgen-api-audit.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>
                [%date{yyyy-MMM-dd HH:mm:ss}] [${hostname}] [pn-pdfgen-api] [ %-5level] %mdc{audit_metadata} %msg%n
            </Pattern>
        </encoder>

        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- rollover daily -->
            <fileNamePattern>${LOG_HOME}/archived/pn-pdfgen-api-audit.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>10MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
    </appender>

    <logger name="AuditLogger" level="info" additivity="false">
        <appender-ref ref="FILE-AUDIT"/>
    </logger>

    <root level="ERROR">
        <appender-ref ref="FILE"/>
        <appender-ref ref="STDOUT"/>
    </root>

</configuration>
