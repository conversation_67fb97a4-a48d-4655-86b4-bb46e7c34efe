package net.plus.pdfgen.util;

import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;

import net.plus.pdfgen.model.PCICSRequestDto;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;
import org.thymeleaf.exceptions.TemplateInputException;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Optional;

import static org.mockito.Mockito.when;

@Slf4j
@RunWith(PowerMockRunner.class)
@PrepareForTest({HtmlUtil.class})
@PowerMockIgnore("*")
public class HtmlUtilTest {

    private HtmlUtil htmlUtil;

    private PCICSRequestDto pcicsRequestDto;

    private TemplateEngine templateEngine;

    private Context context;

    @Before
    public void setUp() {
        htmlUtil = new HtmlUtil();
        templateEngine = PowerMockito.mock(TemplateEngine.class);
        context = PowerMockito.mock(Context.class);
        mockSampleRequest();
    }

    @Test(expected = TemplateInputException.class)
    public void shouldThrowExceptionWhenFileNotFound(){
        htmlUtil.parseHtmlTemplate(pcicsRequestDto);
    }

    @Test
    public void shouldParseHtmlTemplate() throws Exception
    {
        String csHtml = "<!DOCTYPE html><html lang='en' xmlns:th='http://www.thymeleaf.org'>\n"
            + "    <head><meta charset='UTF-8'></head>"
            + "    <body><div><p class='outclass'><b>Contract Summary</b></p></div></body></html>";
        String pciHtml = "<!DOCTYPE html><html lang='en' xmlns:th='http://www.thymeleaf.org'>\n"
            + "    <head><meta charset='UTF-8'></head>"
            + "    <body><div><p class='outclass'><b>Pre-contract Information</b></p></div></body></html>";
        PowerMockito.whenNew(TemplateEngine.class).withNoArguments().thenReturn(templateEngine);
        PowerMockito.whenNew(Context.class).withNoArguments().thenReturn(context);
        when(templateEngine.process("contract_summary_res", context)).thenReturn(csHtml);
        when(templateEngine.process("pre_contract_information_res", context)).thenReturn(pciHtml);
        String html = htmlUtil.parseHtmlTemplate(pcicsRequestDto);
        Assert.assertTrue(html.contains("Contract Summary"));
        Assert.assertTrue(html.contains("Pre-contract Information"));
    }

    private void mockSampleRequest(){
        String createPDFRequest = getTheParseInputRequest();
        Gson gson = new Gson();
        pcicsRequestDto = gson.fromJson(createPDFRequest, PCICSRequestDto.class);
    }

    private String getTheParseInputRequest() {
        final String jsonRequestFileName = "./src/test/resources/input/CreatePdfRequest.json";
        Optional<String> parsedRequest = Optional.empty();
        try {
            parsedRequest = Optional.of(new String(Files.readAllBytes(Paths.get(jsonRequestFileName))));
        } catch (IOException e) {
            log.error("Reading the Json Request from the file failed");
        }
        return parsedRequest.orElse(null);
    }
}
