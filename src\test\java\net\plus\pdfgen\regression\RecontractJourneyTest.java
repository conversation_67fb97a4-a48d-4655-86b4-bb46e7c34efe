package net.plus.pdfgen.regression;

import net.plus.pdfgen.util.HtmlUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.PropertySource;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;

import lombok.extern.slf4j.Slf4j;

import net.plus.pdfgen.PcicsPdfgenApplication;

import java.time.LocalDate;

/**
 * Test class to generate PDF for various recontract journey scenarios,
 * compare it with the sample pdf files
 * and create a new file with differences incase of mismatches
 */
@Slf4j
@RunWith(SpringRunner.class)
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = PcicsPdfgenApplication.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@AutoConfigureMockMvc
@PropertySource("classpath:application-test.properties")
@ActiveProfiles("test")
public class RecontractJourneyTest
{

    private final static String RECONTRACT_JOURNEY = "recontract";

    @Value(("${pcics.stream.url}"))
    private String url;

    @Value("${pcics.file.location}")
    private String fileLocation;

    @Value("${pcics.dir.location}")
    private String dirLocation;

    @Value("${pdf.dir.date.format}")
    private String dateFormat;

    @Autowired
    private HtmlUtil htmlUtil;
    private PdfComparison pdfComparison;

    @Before
    public void initialise() {
        pdfComparison = new PdfComparison(url, dirLocation, fileLocation, dateFormat);
        htmlUtil.setFixedDate(LocalDate.of(2025, 4, 15));
    }

    @Test
    public void dualRecontractWithNoEquipmentPdfComparisonTest()  {
        log.debug("Begin: dualRecontractWithNoEquipmentPdfComparisonTest");
        pdfComparison.initializeParameters(RECONTRACT_JOURNEY, "dual_noequipment");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Dual Recontract with no equipment generated pdf that doesn't match", isEqual);
        log.debug("End: dualRecontractWithNoEquipmentPdfComparisonTest");
    }

    @Test
    public void dualRecontractPdfComparisonTest()  {
        log.debug("Begin: dualRecontractPdfComparisonTest");
        pdfComparison.initializeParameters(RECONTRACT_JOURNEY, "dual_full");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Dual Recontract generated pdf that doesn't match", isEqual);
        log.debug("End: dualRecontractPdfComparisonTest");
    }

    @Test
    public void bbOnlyRecontractPdfComparisonTest()
    {
        log.debug("Begin: bbOnlyRecontractPdfComparisonTest");
        pdfComparison.initializeParameters(RECONTRACT_JOURNEY, "bbonly_full");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("BB Only Recontract generated pdf that doesn't match", isEqual);
        log.debug("End: bbOnlyRecontractPdfComparisonTest");
    }

    @Test
    public void recontractWithUnlimitedFibreExtra()
    {
        log.debug("Begin: recontractWithUnlimitedFibreExtra");
        pdfComparison.initializeParameters(RECONTRACT_JOURNEY, "unlimitedFibreExtra");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Recontract with Unlimited Fibre Extra BB product generated pdf that doesn't match", isEqual);
        log.debug("End: recontractWithUnlimitedFibreExtra");
    }

    @Test
    public void recontractWithUnlimitedFibre()
    {
        log.debug("Begin: recontractWithUnlimitedFibre");
        pdfComparison.initializeParameters(RECONTRACT_JOURNEY, "unlimitedFibre");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Recontract with Unlimited Fibre BB product generated pdf that doesn't match", isEqual);
        log.debug("End: recontractWithUnlimitedFibre");
    }

    @Test
    public void signupWithFullFibre145Bb()
    {
        log.debug("Begin: recontractWithFullFibre145Bb");
        pdfComparison.initializeParameters(RECONTRACT_JOURNEY, "fullFibre145");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Recontract with Full Fibre 145 BB product generated pdf that doesn't match", isEqual);
        log.debug("End: recontractWithFullFibre145Bb");
    }

    @Test
    public void recontractWithUnlimited()
    {
        log.debug("Begin: recontractWithUnlimited");
        pdfComparison.initializeParameters(RECONTRACT_JOURNEY, "unlimited");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Recontract with Unlimited BB product generated pdf that doesn't match", isEqual);
        log.debug("End: recontractWithUnlimited");
    }

    @Test
    public void signupWithFullFibre74Bb()
    {
        log.debug("Begin: recontractWithFullFibre74Bb");
        pdfComparison.initializeParameters(RECONTRACT_JOURNEY, "fullFibre74");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Recontract with Full Fibre 74 BB product generated pdf that doesn't match", isEqual);
        log.debug("End: recontractWithFullFibre74Bb");
    }

    @Test
    public void signupWithFibreBb()
    {
        log.debug("Begin: recontractWithFibreBb");
        pdfComparison.initializeParameters(RECONTRACT_JOURNEY, "fibre");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Recontract with Fibre BB product generated pdf that doesn't match", isEqual);
        log.debug("End: recontractWithFibreBb");
    }

    @Test
    public void signupWithFullFibre500Bb()
    {
        log.debug("Begin: recontractWithFullFibre500Bb");
        pdfComparison.initializeParameters(RECONTRACT_JOURNEY, "fullFibre500");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Recontract with Full Fibre 500 BB product generated pdf that doesn't match", isEqual);
        log.debug("End: recontractWithFullFibre500Bb");
    }

    @Test
    public void signupWithFullFibre300Bb()
    {
        log.debug("Begin: recontractWithFullFibre300Bb");
        pdfComparison.initializeParameters(RECONTRACT_JOURNEY, "fullFibre300");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Recontract with Full Fibre 300 BB product generated pdf that doesn't match", isEqual);
        log.debug("End: recontractWithFullFibre300Bb");
    }

    @Test
    public void signupWithFullFibre900Bb()
    {
        log.debug("Begin: recontractWithFullFibre900Bb");
        pdfComparison.initializeParameters(RECONTRACT_JOURNEY, "fullFibre900");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Recontract with Full Fibre 900 BB product generated pdf that doesn't match", isEqual);
        log.debug("End: recontractWithFullFibre900Bb");
    }

}
