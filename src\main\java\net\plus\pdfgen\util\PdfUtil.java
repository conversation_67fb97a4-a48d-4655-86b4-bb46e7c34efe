package net.plus.pdfgen.util;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Objects;

import com.lowagie.text.DocumentException;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import net.plus.pdfgen.constant.PdfStatus;
import net.plus.pdfgen.dao.PCICSDao;
import net.plus.pdfgen.exception.PdfOperationException;
import net.plus.pdfgen.exception.error.ErrorCode;
import net.plus.pdfgen.model.PCICSRequestDto;

import org.springframework.stereotype.Component;
import org.xhtmlrenderer.layout.SharedContext;
import org.xhtmlrenderer.pdf.ITextRenderer;

@Component
@AllArgsConstructor
@Slf4j
public class PdfUtil
{
    private final FileUtil fileUtil;

    private final PCICSDao pcicsDao;

    public String generatePdfFromHtml(String html, String streamingId, PCICSRequestDto pcicsRequestDto)
    {
        log.debug("Entry: PdfUtil.generatePdfFromHtml()");
        String filePath = fileUtil.getFilePath(streamingId, pcicsRequestDto);
        try (OutputStream outputStream = new FileOutputStream(filePath))
        {

            ITextRenderer renderer = new ITextRenderer();

            SharedContext sharedContext = renderer.getSharedContext();
            sharedContext.setPrint(true);
            sharedContext.setInteractive(false);

            renderer.getFontResolver().addFont(getClass().getClassLoader()
                                                         .getResource(Objects.requireNonNull(
                                                                 "static/pcics/fonts/PlusnetCreightonPro-Bold.otf"))
                                                         .toString(), true);
            renderer.setPDFVersion('7');
            renderer.setDocumentFromString(html);
            renderer.layout();
            renderer.createPDF(outputStream);
        }
        catch (DocumentException| IOException | NullPointerException e)
        {

            pcicsDao.insertOrUpdatePcicsInfoRecord(pcicsRequestDto,
                                                   streamingId,
                                                   filePath,
                                                   PdfStatus.PDFGENFAILED.getStatusId());
            throw new PdfOperationException(ErrorCode.CREATE_PDF_FAILURE,
                                            "generatePdfFromHtml : Exception encountered during HTML to PDF creation",
                                            streamingId,
                                            e);
        }
        log.debug("Exit: PdfUtil.generatePdfFromHtml()");
        return filePath;
    }

    public byte[] convertPDFtoByteStream(String filePath) throws IOException
    {
        Path path = Paths.get(filePath);
        return Files.readAllBytes(path);
    }

}