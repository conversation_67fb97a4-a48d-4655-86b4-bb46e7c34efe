@page {
    size: 1600px;
    display: flex;
    padding: 0;
    background: #FFFFFF;
}
.summary1{
    position: static;
    width: 100%;
    font-family: 'Helvetica';
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    text-align: center;
    font-feature-settings: 'liga' off, 'kern' off;
    color: #4C4C4C;
    flex: none;
    order: 6;
    flex-grow: 0;
    margin-left: 0%;
    margin-bottom: 40px;
    text-indent: -20px; /* Adjust starting point of the text */
}
.heading1{
    position: static;
    width: 892px;
    font-family: 'Plusnet Creighton Pro';
    font-style: normal;
    font-weight: 700;
    font-size: 24px;
    font-feature-settings: 'liga' off, 'kern' off;
    color: #4C4C4C;
    margin-top: 0;
    margin-bottom: 25px;
    flex: none;
    order: 0;
    align-self: stretch;
    flex-grow: 0;
}
.heading2{
    position: static;
    width: 892px;
    height: 24px;
    font-family: 'Plusnet Creighton Pro';
    font-style: normal;
    font-weight: 700;
    font-size: 24px;
    font-feature-settings: 'liga' off, 'kern' off;
    color: #4C4C4C;
    margin-top: 0;
    flex: none;
    order: 0;
    align-self: stretch;
    flex-grow: 0;
    margin-bottom: 15px;
    padding-top: 0;
}
.heading3{
    position: static;
    height: 24px;
    font-family: 'Helvetica';
    font-style: normal;
    font-weight: 700;
    font-size: 18px;
    line-height: 24px;
    font-feature-settings: 'liga' off, 'kern' off;
    color: #4C4C4C;
    flex: none;
    order: 0;
    flex-grow: 0;
    margin-bottom: 18px;
}
.summary2{
    position: static;
    text-align:left;
    font-family: 'Helvetica';
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    font-feature-settings: 'liga' off, 'kern' off;
    color: #4C4C4C;
    flex: none;
    order: 1;
    align-self: stretch;
    flex-grow: 0;
    margin: 0 0 26px 0;
}
.cs_tbl {
    color: black;
    border-collapse: collapse;
    background-color: #FFFFFF;
    border: 1px solid #F2F2F2;
    border-radius: 6px;
    width: 100%;
    flex: none;
    order: 1;
    align-self: stretch;
    flex-grow: 0;
    margin-bottom: 7px;
}
.parent{
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 28px 32px 0 32px;
    margin-left: 17.75%;
    width: 895px;
    background: #F2F2F2;
    border-radius: 8px;
    flex: none;
    order: 8;
    flex-grow: 0;
}
.hr{
    position: static;
    height: 0;
    left: 32px;
    top: 826px;
    color: #4C4C4C;
    border: 0.75px solid #D6D6D6;
    flex: none;
    order: 2;
    align-self: stretch;
    flex-grow: 0;
    margin: 23px 0 20px 0;
}
.outclass{
    font-family: 'Plusnet Creighton Pro';
    position: static;
    height: 20px;
    font-style: normal;
    font-weight: 700;
    font-size: 43px;
    line-height: 20px;
    text-align: center;
    font-feature-settings: 'liga' off, 'kern' off;
    color: #4C4C4C;
    flex: none;
    order: 4;
    flex-grow: 0;
    margin: 38px 0 23.5px 0;
}
table {
    font-family:'Helvetica';
}
.cs_tbl td, #cs_tbl th {
    border: 1px solid #F2F2F2;
    padding: 12px 24px 12px 24px;
    width: 100px;

    height: 48px;
    line-height: 24px;
    text-align: left;
    font-feature-settings: 'liga' off, 'kern' off;
    color: #4C4C4C;
    flex: none;
    order: 0;
    flex-grow: 0;
}
a{
    color: #007390;
}
.a_margin{
    margin-right: -4px;
}
.plusnet_logo{
    width: 86.67px;
    height: 56px;
}
.pagebreak {
    page-break-after: always;
}
.date-top-right {
    position: absolute;
    top: 20px;
    right: 140px; /* Adjust this value to move the date left or right */
    font-family: 'Helvetica'; /* Use the same font family */
    font-style: normal;
    font-weight: 400; /* Adjust weight if needed */
    font-size: 20px; /* Adjust size to match your other text */
    line-height: 24px; /* Adjust line height if needed */
    font-feature-settings: 'liga' off, 'kern' off;
    color: #4C4C4C; /* Use the same color */
}
.custom-table {
    width: 100%;
    border-collapse: collapse;
    border: 0;
    padding-left: 0;
}
.custom-table td {
    border: none;
    padding-left: 0;
}
