# Pdf generation API

> A REST based micro-service for generating PDFs on request

---

# Table of Contents

1. [Pre-Requisites](#pre-requisites)
2. [Clone](#clone)
3. [Setup](#setup)
4. [Docker](#docker)
5. [Version](#version-info)
6. [Documentation](#documentation)

---

## Pre-Requisites

- Install Docker on your laptop: https://www.docker.com/
- Install Lombok plugin in IntelliJ
- Install Java 11 JDK https://docs.aws.amazon.com/corretto/latest/corretto-11-ug/macos-install.html
- Add Java 11 to Maven Toolchains file in ~/.m2/toolchains.xml
- Add Java 11 JDK to Intellij
- Update project in Intellij to use Java 11

### Clone

- Clone this repo to your local machine using `ssh://**************************:7999/billing/pn-pdfgen-api.git`

### Setup

```shell
$ mvn clean install
```

### Docker

To 'dockerise' this module, the following steps were done:

- Add a Dockerfile to the root of the module
- Add configuration to the pom.xml
- Add a yaml file in the 'conf' directory
- Add a bootstrap.sh in the 'conf' directory

### Version info and health

> The PDE/IE load balancer is set up to redirect traffic from https://api.int.plus.net/pdfgen to the API on the PDE/IE. It runs in Docker on the root container (container01 in LTOR terminology). If using the following URLs in a browser, make sure your proxy settings are pointing to your PDE/IE.

```shell
https://api.int.plus.net/actuator/info
https://api.int.plus.net/actuator/health
```

or locally:

```shell
http://localhost:8089/actuator/info
http://localhost:8089/actuator/health
```

******This is a dockerised spring boot app for pdfgen api.******

**To build the docker file locally**
Note - not sure this does anything different to just running 'mvn clean install'

```
mvn clean install dockerfile:build
```

### Running Locally

You can run the Application via Intellij in the normal way. 
It requires passing your PDE IP address as a VM argument and update to logging level to a preferred level when running PdfgenApi locally:
```
coredb-datasource.jdbcUrl
coredb-datasource.password
logging.level.net.plus.pdfgen
```
Also, update streaming url to below: 
```
pcics.stream.url=http://localhost:8089/pdfgen/v1/pcics
```

The PdfgenApi endpoints can now be called like this (although you'll need to add the appropriate headers and, when
appropriate, the request body):

```
http://localhost:8089/pdfgen/v1/pcics/{streamingId}
```

### Docker images

Once you have run an 'mvn clean install' you might want to push your docker image to your PDE
(for example for feature testing, or to run TestAutomation on it). Obtain the image id (either from the output from
the 'mvn clean install') or:

```shell
docker images
docker tag [IMAGE_ID] docker-registry.env.plus.net/plusnet/pn-pdfgen-api:[VERSION]-[BRANCH]
docker push docker-registry.env.plus.net/plusnet/pn-pdfgen-api:[VERSION]-[BRANCH]
```

For example:

```shell
docker images
docker tag b56a5b4dd642 docker-registry.env.plus.net/plusnet/pn-pdfgen-api:1.1.0-feature-BD-5028-dockerize-pdfgen-api-SNAPSHOT
docker push docker-registry.env.plus.net/plusnet/pn-pdfgen-api:1.1.0-feature-BD-5028-dockerize-pdfgen-api-SNAPSHOT
```

This Pushed the image to harbor dashboard, you can find
this: https://docker-registry.env.plus.net/harbor/projects/13/repositories/plusnet%2Fpn-pdfgen-api

Once you have checked that the snapshot is indeed on the harbor dashboard, you will need to edit the pn-pdfgen-api.yaml
file on your PDE on the root platform.

```shell
pde connect root
cd /etc/pn-pdfgen-api/
vi pn-pdfgen-api.yaml
```

You need to edit the image configuration to with the name of the snapshot you wish to use:

```shell
# image: ${DOCKER_REGISTRY}/plusnet/pn-pdfgen-api:1.1.0
image: docker-registry.env.plus.net/plusnet/pn-pdfgen-api:1.1.0-feature-BD-5028-dockerize-pdfgen-api-SNAPSHOT
```

WARNING: You must be careful as you must leave four spaces before the image configuration is written out as yaml is
space sensitive.

Once this is done you must now deploy the image with you new yaml configuration:

```shell
docker stack deploy -c /etc/pn-pdfgen-api/pn-pdfgen-api.yaml pdfgen
```

If you cannot push and get access denied, then you need to login:

``` shell
docker login docker-registry.env.plus.net
```

---
