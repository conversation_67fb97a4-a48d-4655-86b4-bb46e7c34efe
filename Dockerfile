ARG BASE_IMAGE
FROM $BASE_IMAGE as baseImage
LABEL SERVICE="PN-PDFGEN-API"

# Update packages
RUN yum -y update && rm -rf /var/cache/yum/* && yum -y clean all &&\
    rm -rf /etc/localtime && ln -fs /usr/share/zoneinfo/Europe/London /etc/localtime && date +%Z &&\
    groupadd -r -g 62310 pdfgen && useradd -r -s /bin/false -g pdfgen -u 62310 pdfgen

FROM baseImage
# Jar copied to root location and billinguser ownership applied
ARG JAR_NAME
COPY --chown=pdfgen:pdfgen target/$JAR_NAME.jar /pn-pdfgen-api.jar

WORKDIR /usr/local/bin/

# Bootstrap file copied and permissions changed
COPY --chown=pdfgen:pdfgen target/conf/bootstrap.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/bootstrap.sh

# Healthcheck file copied and permissions changed
COPY --chown=pdfgen:pdfgen target/conf/healthcheck.sh /usr/local/bin/

# Creation of log directory and permission changed
RUN mkdir -p /var/log/pn-pdfgen-api &&\
    mkdir -p /share/pdfgen/pcics &&\
    chown -R pdfgen:pdfgen /var/log/pn-pdfgen-api &&\
    chown -R pdfgen:pdfgen /share/pdfgen &&\
    chmod -R 755 /var/log/pn-pdfgen-api &&\
    chmod -R 755 /share/pdfgen

# Expose pn-pdfgen-api service port
EXPOSE 8089

# Add Healthcheck
HEALTHCHECK --interval=1m --timeout=30s --retries=5 --start-period=2m \
  CMD bash healthcheck.sh || exit 1

# Runtime
USER pdfgen
ENTRYPOINT ["/usr/local/bin/bootstrap.sh"]
