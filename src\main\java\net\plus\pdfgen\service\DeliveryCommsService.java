package net.plus.pdfgen.service;

import net.plus.pdfgen.exception.StreamingIdNotFoundExeception;
import net.plus.pdfgen.exception.error.ErrorCode;

import org.springframework.stereotype.Service;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import net.plus.pdfgen.dao.PCICSDao;
import net.plus.pdfgen.model.DeliveryCommsDto;

@Service
@AllArgsConstructor
@Slf4j
public class DeliveryCommsService
{

    private final PCICSDao pcicsDao;

    public void updateDeliveryCommsStatus(String streamingId, DeliveryCommsDto deliveryCommsDto)
    {
        log.debug("Entry: PCICSDeliveryService.updateDeliveryCommsStatus()");

        if (pcicsDao.isStreamingIdExist(streamingId))
        {
            pcicsDao.insertOrUpdateDeliveryCommsDetails(streamingId, deliveryCommsDto);
        }
        else
        {
            throw new StreamingIdNotFoundExeception(ErrorCode.STREAMING_ID_NOT_FOUND,
                                                    "updateDeliveryCommsStatus : Unable to find input streamingId in pcics_info",
                                                    streamingId);
        }

        log.debug("Exit: PCICSDeliveryService.updateDeliveryCommsStatus()");
    }
}