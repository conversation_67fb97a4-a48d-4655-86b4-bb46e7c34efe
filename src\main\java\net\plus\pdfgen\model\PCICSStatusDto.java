package net.plus.pdfgen.model;

import javax.validation.constraints.NotNull;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import net.plus.pdfgen.exception.error.ErrorCode;

@ToString
@Getter
@Setter
@NoArgsConstructor
@XmlRootElement(name = "pcicsstatus",
                namespace = "http://www.plus.net/pcicsstatus")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(namespace = "http://www.plus.net/pcicsstatus")
public class PCICSStatusDto
{

    private String orderId;

    @XmlElement(required = true)
    @NotNull(message = ErrorCode.MISSING_ACCOUNTID)
    private Integer accountId;

}