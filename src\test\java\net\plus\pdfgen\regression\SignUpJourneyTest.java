package net.plus.pdfgen.regression;

import net.plus.pdfgen.util.HtmlUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.PropertySource;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;

import lombok.extern.slf4j.Slf4j;

import net.plus.pdfgen.PcicsPdfgenApplication;

import java.time.LocalDate;

/**
 * Test class to generate PDF for various signup journey scenarios,
 * compare it with the sample pdf files
 * and create a new file with differences incase of mismatches
 */
@Slf4j
@RunWith(SpringRunner.class)
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = PcicsPdfgenApplication.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@AutoConfigureMockMvc
@PropertySource("classpath:application-test.properties")
@ActiveProfiles("test")
public class SignUpJourneyTest
{

    private final static String SIGNUP_JOURNEY = "signup";

    @Value(("${pcics.stream.url}"))
    private String url;

    @Value("${pcics.file.location}")
    private String fileLocation;

    @Value("${pcics.dir.location}")
    private String dirLocation;

    @Value("${pdf.dir.date.format}")
    private String dateFormat;

    @Autowired
    private HtmlUtil htmlUtil;

    private PdfComparison pdfComparison;

    @Before
    public void initialise() {
        pdfComparison = new PdfComparison(url, dirLocation, fileLocation, dateFormat);
        htmlUtil.setFixedDate(LocalDate.of(2025, 1, 15));
    }

    @Test
    public void dualSignUpWithFreeCallFeaturePdfComparisonTest()  {
        log.debug("Begin: dualSignUpWithFreeCallFeaurePdfComparisonTest");
        pdfComparison.initializeParameters(SIGNUP_JOURNEY, "dual_freecallfeature");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Dual SignUp with Free CallFeature generated pdf that doesn't match", isEqual);
        log.debug("End: dualSignUpWithFreeCallFeaturePdfComparisonTest");
    }

    @Test
    public void dualSignUpWithPaidCallFeaturePdfComparisonTest()  {
        log.debug("Begin: dualSignUpWithPaidCallFeaturePdfComparisonTest");
        pdfComparison.initializeParameters(SIGNUP_JOURNEY, "dual_paidcallfeature");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Dual SignUp with Paid CallFeature generated pdf that doesn't match", isEqual);
        log.debug("End: dualSignUpWithPaidCallFeaturePdfComparisonTest");
    }

    @Test
    public void dualSignUpPdfComparisonTest()
    {
        log.debug("Begin: dualSignUpPdfComparisonTest");
        pdfComparison.initializeParameters(SIGNUP_JOURNEY, "dual_full");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Dual SignUp generated pdf that doesn't match", isEqual);
        log.debug("End: dualSignUpPdfComparisonTest");
    }

    @Test
    public void signupWithBbUnlimited()
    {
        log.debug("Begin: signupWithBbUnlimited");
        pdfComparison.initializeParameters(SIGNUP_JOURNEY, "unlimited");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Signup with Unlimited BB product generated pdf that doesn't match", isEqual);
        log.debug("End: signupWithBbUnlimited");
    }

    @Test
    public void signupWithFibreBb()
    {
        log.debug("Begin: signupWithFibreBb");
        pdfComparison.initializeParameters(SIGNUP_JOURNEY, "fibre");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Signup with Fibre BB product generated pdf that doesn't match", isEqual);
        log.debug("End: signupWithFibreBb");
    }

    @Test
    public void signupWithFullFibre900Bb()
    {
        log.debug("Begin: signupWithFullFibre900Bb");
        pdfComparison.initializeParameters(SIGNUP_JOURNEY, "fullFibre900");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Signup with Full Fibre 900 BB product generated pdf that doesn't match", isEqual);
        log.debug("End: signupWithFullFibre900Bb");
    }

    @Test
    public void signupWithFullFibre300Bb()
    {
        log.debug("Begin: signupWithFullFibre300Bb");
        pdfComparison.initializeParameters(SIGNUP_JOURNEY, "fullFibre300");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Signup with Full Fibre 300 BB product generated pdf that doesn't match", isEqual);
        log.debug("End: signupWithFullFibre300Bb");
    }

    @Test
    public void signupWithFullFibre145Bb()
    {
        log.debug("Begin: signupWithFullFibre145Bb");
        pdfComparison.initializeParameters(SIGNUP_JOURNEY, "fullFibre145");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Signup with Full Fibre 145 BB product generated pdf that doesn't match", isEqual);
        log.debug("End: signupWithFullFibre145Bb");
    }

    @Test
    public void signupWithFullFibre500Bb()
    {
        log.debug("Begin: signupWithFullFibre500Bb");
        pdfComparison.initializeParameters(SIGNUP_JOURNEY, "fullFibre500");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Signup with Full Fibre 500 BB product generated pdf that doesn't match", isEqual);
        log.debug("End: signupWithFullFibre500Bb");
    }

    @Test
    public void signupWithFullFibre74Bb()
    {
        log.debug("Begin: signupWithFullFibre74Bb");
        pdfComparison.initializeParameters(SIGNUP_JOURNEY, "fullFibre74");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Signup with Full Fibre 74 BB product generated pdf that doesn't match", isEqual);
        log.debug("End: signupWithFullFibre74Bb");
    }

}
