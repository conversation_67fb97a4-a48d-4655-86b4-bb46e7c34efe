#!/bin/bash
set -e

PN_PDFGEN_API_HOST_FILE=/etc/hostname
APPLICATION_NAME=pn-pdfgen-api

PN_PDFGEN_API_JAVA_AGENT_STRING=""

PDFGEN_TRUSTSTORE_PASS=$(cat /run/secrets/PDFGEN_TRUSTSTORE_PASS)

PN_PDFGEN_API_JAVA_OPTIONS="-Xmx2048m -XX:+UseG1GC -XX:+UseStringDeduplication -Djavax.net.ssl.trustStore=/keystore/truststore.jks -Djavax.net.ssl.trustStorePassword=${PDFGEN_TRUSTSTORE_PASS} ${PN_PDFGEN_API_JAVA_AGENT_STRING}"

exec java $PN_PDFGEN_API_JAVA_OPTIONS -jar /${APPLICATION_NAME}.jar
