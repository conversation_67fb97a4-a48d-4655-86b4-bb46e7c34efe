package net.plus.pdfgen.regression;

import net.plus.pdfgen.util.HtmlUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.PropertySource;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit4.SpringRunner;

import lombok.extern.slf4j.Slf4j;

import net.plus.pdfgen.PcicsPdfgenApplication;

import java.time.LocalDate;

/**
 * Test class to generate PDF for various regrade journey scenarios,
 * compare it with the sample pdf files
 * and create a new file with differences incase of mismatches
 */
@Slf4j
@RunWith(SpringRunner.class)
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = PcicsPdfgenApplication.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@AutoConfigureMockMvc
@PropertySource("classpath:application-test.properties")
@ActiveProfiles("test")
public class RegradeJourneyTest
{

    private final static String REGRADE_JOURNEY = "regrade";

    @Value(("${pcics.stream.url}"))
    private String url;

    @Value("${pcics.file.location}")
    private String fileLocation;

    @Value("${pcics.dir.location}")
    private String dirLocation;

    @Value("${pdf.dir.date.format}")
    private String dateFormat;

    @Autowired
    private HtmlUtil htmlUtil;

    private PdfComparison pdfComparison;

    @Before
    public void initialise() {
        pdfComparison = new PdfComparison(url, dirLocation, fileLocation, dateFormat);
        htmlUtil.setFixedDate(LocalDate.of(2025, 3, 1));
    }

    @Test
    public void bbOnlyRegradeWithNoOtcPdfComparisonTest()  {
        log.debug("Begin: bbOnlyRegradeWithNoOtcPdfComparisonTest");
        pdfComparison.initializeParameters(REGRADE_JOURNEY, "bbonly_nootc");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("BB Only Regrade with no OTC generated pdf that doesn't match", isEqual);
        log.debug("End: bbOnlyRegradeWithNoOtcPdfComparisonTest");
    }

    @Test
    public void bbOnlyRegradeWithNoMgsPdfComparisonTest()  {
        log.debug("Begin: bbOnlyRegradeWithNoMgsPdfComparisonTest");
        pdfComparison.initializeParameters(REGRADE_JOURNEY, "bbonly_nomgs");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("BB Only Regrade with no MGS generated pdf that doesn't match", isEqual);
        log.debug("End: bbOnlyRegradeWithNoMgsPdfComparisonTest");
    }

    @Test
    public void bbOnlyRegradePdfComparisonTest()
    {
        log.debug("Begin: bbOnlyRegradePdfComparisonTest");
        pdfComparison.initializeParameters(REGRADE_JOURNEY, "bbonly_full");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("BB Only Regrade generated pdf that doesn't match", isEqual);
        log.debug("End: bbOnlyRegradePdfComparisonTest");
    }

    @Test
    public void regradeWithFullFibre900Bb()
    {
        log.debug("Begin: regradeWithFullFibre900Bb");
        pdfComparison.initializeParameters(REGRADE_JOURNEY, "fullFibre900");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Regrade with Full Fibre 900 BB product generated pdf that doesn't match", isEqual);
        log.debug("End: regradeWithFullFibre900Bb");
    }

    @Test
    public void regradeWithFibreBb()
    {
        log.debug("Begin: regradeWithFibreBb");
        pdfComparison.initializeParameters(REGRADE_JOURNEY, "fibre");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Regrade with Fibre BB product generated pdf that doesn't match", isEqual);
        log.debug("End: regradeWithFibreBb");
    }

    @Test
    public void regradeWithFullFibre500Bb()
    {
        log.debug("Begin: regradeWithFullFibre500Bb");
        pdfComparison.initializeParameters(REGRADE_JOURNEY, "fullFibre500");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Regrade with Full Fibre 500 BB product generated pdf that doesn't match", isEqual);
        log.debug("End: regradeWithFullFibre500Bb");
    }

    @Test
    public void regradeWithFullFibre74Bb()
    {
        log.debug("Begin: regradeWithFullFibre74Bb");
        pdfComparison.initializeParameters(REGRADE_JOURNEY, "fullFibre74");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Regrade with Full Fibre 74 BB product generated pdf that doesn't match", isEqual);
        log.debug("End: regradeWithFullFibre74Bb");
    }

    @Test
    public void regradeWithFullFibre145Bb()
    {
        log.debug("Begin: regradeWithFullFibre145Bb");
        pdfComparison.initializeParameters(REGRADE_JOURNEY, "fullFibre145");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Regrade with Full Fibre 145 BB product generated pdf that doesn't match", isEqual);
        log.debug("End: regradeWithFullFibre145Bb");
    }

    @Test
    public void regradeWithFullFibre300Bb()
    {
        log.debug("Begin: regradeWithFullFibre300Bb");
        pdfComparison.initializeParameters(REGRADE_JOURNEY, "fullFibre300");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Regrade with Full Fibre 300 BB product generated pdf that doesn't match", isEqual);
        log.debug("End: regradeWithFullFibre300Bb");
    }

    @Test
    public void regradeWithUnlimitedFibreBb()
    {
        log.debug("Begin: regradeWithUnlimitedFibreBb");
        pdfComparison.initializeParameters(REGRADE_JOURNEY, "unlimitedFibre");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Regrade with Unlimited Fibre BB product generated pdf that doesn't match", isEqual);
        log.debug("End: regradeWithUnlimitedFibreBb");
    }

    @Test
    public void regradeWithUnlimitedFibreExtraBb()
    {
        log.debug("Begin: regradeWithUnlimitedFibreExtraBb");
        pdfComparison.initializeParameters(REGRADE_JOURNEY, "unlimitedFibreExtra");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Regrade with Unlimited Fibre Extra BB product generated pdf that doesn't match", isEqual);
        log.debug("End: regradeWithUnlimitedFibreExtraBb");
    }

    @Test
    public void regradeWithUnlimitedBb()
    {
        log.debug("Begin: regradeWithUnlimitedBb");
        pdfComparison.initializeParameters(REGRADE_JOURNEY, "unlimited");
        boolean isEqual = pdfComparison.generatePdfAndCompare();
        Assert.assertTrue("Regrade with Unlimited BB product generated pdf that doesn't match", isEqual);
        log.debug("End: regradeWithUnlimitedBb");
    }

}
