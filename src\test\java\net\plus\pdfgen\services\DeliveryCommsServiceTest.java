package net.plus.pdfgen.services;


import com.google.gson.Gson;

import net.plus.pdfgen.dao.PCICSDao;
import net.plus.pdfgen.exception.StreamingIdNotFoundExeception;
import net.plus.pdfgen.model.DeliveryCommsDto;
import net.plus.pdfgen.model.PCICSRequestDto;
import net.plus.pdfgen.service.DeliveryCommsService;
import net.plus.pdfgen.util.FileUtil;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.PropertySource;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;

import lombok.extern.slf4j.Slf4j;

import static org.mockito.Mockito.*;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
public class DeliveryCommsServiceTest {

    String streamingId = "0a49af44fb126c85d18e54a7a3fbd959";

    @Mock
    private PCICSDao pcicsDao;

    private DeliveryCommsService deliveryCommsService;

    DeliveryCommsDto deliveryCommsDto;

    @Before
    public void setUp()
    {
        deliveryCommsService = new DeliveryCommsService(pcicsDao);
        mockSampleRequest();
    }

    @Test
    public void shouldUpdateDeliveryCommsStatus(){
        when(pcicsDao.isStreamingIdExist(streamingId)).thenReturn(true);
        deliveryCommsService.updateDeliveryCommsStatus(streamingId, deliveryCommsDto);
        verify(pcicsDao, times(1)).insertOrUpdateDeliveryCommsDetails(streamingId, deliveryCommsDto);
    }

    @Test(expected = StreamingIdNotFoundExeception.class)
    public void shouldThrowExceptionWhenSteamingIdNotFound(){
        when(pcicsDao.isStreamingIdExist(streamingId)).thenReturn(false);
        deliveryCommsService.updateDeliveryCommsStatus(streamingId, deliveryCommsDto);
        verify(pcicsDao, never()).insertOrUpdateDeliveryCommsDetails(streamingId, deliveryCommsDto);
    }

    public void mockSampleRequest(){
        DeliveryCommsDto deliveryCommsDto = new DeliveryCommsDto();
        deliveryCommsDto.setDeliveryType("EMAIL");
        deliveryCommsDto.setStatus("SUCCESS");
        deliveryCommsDto.setHandle("PCICS");
        deliveryCommsDto.setOrderId("12345");
    }
}
