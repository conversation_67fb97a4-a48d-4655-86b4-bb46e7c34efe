#!/usr/bin/env bash

CMD="curl -i -H 'accept:application/json' http://localhost:8089/actuator/health"

HEALTH_RESPONSE=`${CMD}`
printf "Healthcheck Response:\n${HEALTH_RESPONSE}\n"

HEALTH_RESPONSE_STATUS=`echo "${HEALTH_RESPONSE}" | head -1`
printf "Healthcheck Response Status: \n${HEALTH_RESPONSE_STATUS}\n"

HEALTH_RESPONSE_STATUS_CODE=`echo "${HEALTH_RESPONSE_STATUS}" | awk '{print $2}'`
printf "Healthcheck Response status code: \n${HEALTH_RESPONSE_STATUS_CODE}\n"

if [[ "${HEALTH_RESPONSE_STATUS_CODE}" == "200" ]]; then
   echo "Healthcheck status code: 200 OK"
else
   printf "ERROR HEALTHCHECK FAILED: curl: ${HEALTH_RESPONSE_STATUS}\n"
   exit 1
fi

HEALTH_RESPONSE_BODY=`echo "${HEALTH_RESPONSE}" | tail -1`
printf "Healthcheck Response Body:\n${HEALTH_RESPONSE_BODY}\n"

PARSED_STATUS=`echo "${HEALTH_RESPONSE_BODY}" | python -c "exec(\"import sys, json\nstatus=json.load(sys.stdin)['status']\nprint(status)\")"`

if [[ "${PARSED_STATUS}" == "UP" ]]; then
   echo "HEALTHCHECK: UP OK"
else
   echo "ERROR HEALTHCHECK FAILED: ${PARSED_STATUS}"
   exit 1
fi

exit 0
