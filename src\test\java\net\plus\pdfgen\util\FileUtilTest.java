package net.plus.pdfgen.util;

import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;

import net.plus.pdfgen.constant.PdfStatus;
import net.plus.pdfgen.dao.PCICSDao;
import net.plus.pdfgen.exception.SharedDirectoryCreationException;
import net.plus.pdfgen.model.PCICSRequestDto;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.internal.verification.Times;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.InvalidPathException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Optional;

import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

@Slf4j
@RunWith(PowerMockRunner.class)
@PrepareForTest({FileUtil.class})
@PowerMockIgnore("*")
public class FileUtilTest {

    String streamingId = "0a49af44fb126c85d18e54a7a3fbd959";

    @Mock
    private PCICSDao pcicsDao;

    private FileUtil fileUtil;

    private File file;

    private PCICSRequestDto pcicsRequestDto;

    @Before
    public void setUp()
    {
        fileUtil = new FileUtil(pcicsDao);
        mockSampleRequest();
    }

    @Test
    public void shouldGetFilePathOfNewDirectoryCreated() throws Exception
    {
        String expectedPath = intialiseVariables();
        when(file.exists()).thenReturn(false);
        String directoryPath = fileUtil.getFilePath(streamingId, pcicsRequestDto);
        verify(file, times(1)).mkdirs();
        verify(pcicsDao, never()).insertOrUpdatePcicsInfoRecord(pcicsRequestDto,
                                                                streamingId,
                                                                null,
                                                                PdfStatus.PDFGENFAILED.getStatusId());
        Assert.assertEquals(directoryPath, expectedPath);
    }

    @Test
    public void shouldGetFilePathOfExistingDirectory() throws Exception
    {
        String expectedPath = intialiseVariables();
        when(file.exists()).thenReturn(true);
        String directoryPath = fileUtil.getFilePath(streamingId, pcicsRequestDto);
        verify(file, times(1)).exists();
        verify(file, never()).mkdirs();
        verify(pcicsDao, never()).insertOrUpdatePcicsInfoRecord(pcicsRequestDto,
                                                                streamingId,
                                                                null,
                                                                PdfStatus.PDFGENFAILED.getStatusId());
        Assert.assertEquals(directoryPath, expectedPath);
    }

    @Test(expected = SharedDirectoryCreationException.class)
    public void shouldThrowExceptionWhenDirIsCreated() throws Exception
    {
        String expectedPath = intialiseVariables();
        when(file.mkdirs()).thenThrow(SecurityException.class);
        String directoryPath = fileUtil.getFilePath(streamingId, pcicsRequestDto);
        verify(file, times(1)).mkdirs();
        verify(pcicsDao, times(1)).insertOrUpdatePcicsInfoRecord(pcicsRequestDto,
                                                                streamingId,
                                                                null,
                                                                PdfStatus.PDFGENFAILED.getStatusId());
        Assert.assertEquals(directoryPath, expectedPath);
    }

    private String intialiseVariables() throws Exception
    {
        String basDir = "./src/test/resources/";
        String format = "yyyy/MM/dd";
        SimpleDateFormat dateFormat = new SimpleDateFormat(format);
        String expectedPath = new StringBuffer(basDir)
                .append(dateFormat.format(new Date()))
                .append("/RES/PCI_CS_")
                .append(streamingId)
                .append(".pdf")
                .toString();
        Whitebox.setInternalState(fileUtil, "dirDateFormat", format);
        Whitebox.setInternalState(fileUtil, "baseDirPath", basDir);
        file = PowerMockito.mock(File.class);
        PowerMockito.whenNew(File.class).withAnyArguments().thenReturn(file);
        return expectedPath;
    }

    @Test
    public void shouldValidateAndDeleteFile() throws IOException
    {
        String fileName = "sample.pdf";
        mockStatic(Files.class);
        boolean isTrue = fileUtil.validateAndDeleteFile(streamingId, fileName);
        Assert.assertTrue(isTrue);
    }

    @Test
    public void shouldReturnFalseWhenExceptionIsThrown() throws Exception
    {
        String fileName = "sample.pdf";
        mockStatic(Files.class, Paths.class);
        when(Paths.get(fileName)).thenThrow(new InvalidPathException(fileName, "Invalid file path"));
        boolean isFalse = fileUtil.validateAndDeleteFile(streamingId, fileName);
        Assert.assertFalse(isFalse);
    }

    public void mockSampleRequest(){
        String createPDFRequest = getTheParseInputRequest();
        Gson gson = new Gson();
        pcicsRequestDto = gson.fromJson(createPDFRequest, PCICSRequestDto.class);
    }

    private String getTheParseInputRequest() {

        final String jsonRequestFileName = "./src/test/resources/input/CreatePdfRequest.json";

        Optional<String> parsedRequest = Optional.empty();
        try {
            parsedRequest = Optional.of(new String(Files.readAllBytes(Paths.get(jsonRequestFileName))));
        } catch (IOException e) {
            log.error("Reading the Json Request from the file is failed");
        }
        return parsedRequest.orElse(null);
    }
}
