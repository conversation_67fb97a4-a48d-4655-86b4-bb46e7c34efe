package net.plus.pdfgen.util;

import lombok.extern.slf4j.Slf4j;
import net.plus.pdfgen.model.PCICSRequestDto;

import org.htmlcleaner.CleanerProperties;
import org.htmlcleaner.HtmlCleaner;
import org.htmlcleaner.PrettyXmlSerializer;
import org.htmlcleaner.XmlSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;
import org.thymeleaf.templatemode.TemplateMode;
import org.thymeleaf.templateresolver.ClassLoaderTemplateResolver;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Component
@Slf4j
public class HtmlUtil {

    @Value("${template.path}")
    private String templatePath;

    private LocalDate fixedDate;

    public void setFixedDate(LocalDate fixedDate) {
        this.fixedDate = fixedDate;
    }

    public String parseHtmlTemplate(PCICSRequestDto pcicsInfo){
        log.debug("Entry: HtmlUtil.parseHtmlTemplate()");
        ClassLoaderTemplateResolver templateResolver = new ClassLoaderTemplateResolver();
        templateResolver.setPrefix(templatePath);
        templateResolver.setSuffix(".html");
        templateResolver.setTemplateMode(TemplateMode.HTML);

        TemplateEngine templateEngine = new TemplateEngine();
        templateEngine.setTemplateResolver(templateResolver);

        Context context = new Context();
        context.setVariable("pcicsInfo", pcicsInfo);

        LocalDate currentDate = (fixedDate != null) ? fixedDate : LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        String formattedDate = currentDate.format(formatter);
        context.setVariable("currentDate", formattedDate);

        String csHtml= templateEngine.process("contract_summary_res", context);
        String pciHtml= templateEngine.process("pre_contract_information_res", context);

        HtmlCleaner cleaner = new HtmlCleaner();
        CleanerProperties cleanerProperties = cleaner.getProperties();
        XmlSerializer xmlSerializer = new PrettyXmlSerializer(cleanerProperties);

        Document document = Jsoup.parse(csHtml, "UTF-8");
        document.append(pciHtml);
        document.outputSettings().syntax(Document.OutputSettings.Syntax.xml);
        log.debug("Exit: HtmlUtil.parseHtmlTemplate()");
        return xmlSerializer.getAsString(document.html());
    }

}
