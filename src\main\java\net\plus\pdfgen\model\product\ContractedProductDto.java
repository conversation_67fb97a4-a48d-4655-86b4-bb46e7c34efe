package net.plus.pdfgen.model.product;

import java.math.BigDecimal;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import net.plus.pdfgen.deserializer.CurrencyDeserializer;
import net.plus.pdfgen.exception.error.ErrorCode;

@ToString(callSuper = true)
@Getter
@Setter
@NoArgsConstructor
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(namespace = "http://www.plus.net/pcicsrequest")
public class ContractedProductDto extends ContractDto
{
    @XmlElement(required = true)
    @NotEmpty(message = ErrorCode.MISSING_C2M_PRODUCTNAME)
    private String c2mProductName;

    @XmlElement(required = true)
    @NotEmpty(message = ErrorCode.MISSING_PRODUCT_DISPLAY_NAME)
    private String productDisplayName;

    @XmlElement(required = true)
    @NotNull(message = ErrorCode.MISSING_DISCOUNTEDPRICE)
    @JsonDeserialize(using = CurrencyDeserializer.class)
    private BigDecimal discountedPrice;

    @XmlElement(required = true)
    @NotNull(message = ErrorCode.MISSING_PRODUCTPRICE)
    @JsonDeserialize(using = CurrencyDeserializer.class)
    private BigDecimal fullPrice;

}