{"openapi": "3.0.1", "info": {"title": "PCICS PDF Generation API", "description": "PCICS PDF Generation API documentation ", "version": "1.68.3"}, "servers": [{"url": "http://localhost:8089/", "description": "Generated server url"}], "tags": [{"name": "Delivery comms API", "description": "Manages delivery comms status"}, {"name": "Pre Contract Information and Contract Summary", "description": "Manage PCICS PDF generation"}], "paths": {"/pdfgen/v1/pcics/{streamingId}": {"get": {"tags": ["Pre Contract Information and Contract Summary"], "description": "Retrieves the PCICS PDF", "operationId": "retrievePdf", "parameters": [{"name": "streamingId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"400": {"description": "<ERROR_DESCRIPTION>", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseDto"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponseDto"}}}}, "500": {"description": "Internal server error, <ERROR_DESCRIPTION>", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseDto"}}}}, "404": {"description": "Streaming ID not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseDto"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"type": "string", "format": "byte"}}}}}}}, "put": {"tags": ["Pre Contract Information and Contract Summary"], "description": "Updates the PCICS PDF status in Core DB", "operationId": "updateJourneyCompletionStatus", "parameters": [{"name": "streamingId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PCICSStatusDto"}}}, "required": true}, "responses": {"400": {"description": "<ERROR_DESCRIPTION>", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseDto"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponseDto"}}}}, "500": {"description": "Internal server error, <ERROR_DESCRIPTION>", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseDto"}}}}, "404": {"description": "Streaming ID not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseDto"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/pdfgen/v1/pcics/delivery-comms/{streamingId}": {"put": {"tags": ["Delivery comms API"], "description": "Update delivery comms status", "operationId": "updateDeliveryCommsStatus", "parameters": [{"name": "streamingId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeliveryCommsDto"}}}, "required": true}, "responses": {"400": {"description": "<ERROR_DESCRIPTION>", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseDto"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponseDto"}}}}, "500": {"description": "Internal server error, <ERROR_DESCRIPTION>", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseDto"}}}}, "404": {"description": "Streaming ID not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseDto"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"type": "string"}}}}}}}, "/pdfgen/v1/pcics": {"post": {"tags": ["Pre Contract Information and Contract Summary"], "description": "Generates the PCICS PDF ", "operationId": "createPdf", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PCICSRequestDto"}}}, "required": true}, "responses": {"400": {"description": "<ERROR_DESCRIPTION>", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseDto"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponseDto"}}}}, "500": {"description": "Internal server error, <ERROR_DESCRIPTION>", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseDto"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponseDto"}}}}, "201": {"description": "CREATED", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PCICSResponseDto"}}}}}}}}, "components": {"schemas": {"ErrorResponseDto": {"type": "object", "properties": {"id": {"type": "string"}, "message": {"type": "string"}}}, "PCICSStatusDto": {"required": ["accountId"], "type": "object", "properties": {"orderId": {"type": "string"}, "accountId": {"type": "string"}}, "xml": {"name": "p<PERSON><PERSON><PERSON><PERSON>", "namespace": "http://www.plus.net/pcicsstatus"}}, "DeliveryCommsDto": {"required": ["deliveryType", "status"], "type": "object", "properties": {"orderId": {"type": "string"}, "handle": {"type": "string"}, "deliveryType": {"type": "string"}, "status": {"type": "string"}}, "xml": {"name": "deliverycomms", "namespace": "http://www.plus.net/deliverycomms"}}, "AddOns": {"required": ["fullPrice", "productName"], "type": "object", "properties": {"productName": {"type": "string"}, "fullPrice": {"type": "number"}}}, "BroadbandProductDto": {"required": ["discountedPrice", "fullPrice", "productName"], "type": "object", "properties": {"contractDuration": {"type": "integer", "format": "int32"}, "productName": {"type": "string"}, "discountedPrice": {"type": "number"}, "fullPrice": {"type": "number"}, "estimatedDownloadSpeedMin": {"type": "string"}, "estimatedDownloadSpeedMax": {"type": "string"}, "estimatedUploadSpeedMin": {"type": "string"}, "estimatedUploadSpeedMax": {"type": "string"}, "minGuaranteedSpeed": {"type": "string"}}}, "CallFeatureDto": {"required": ["fullPrice", "productName"], "type": "object", "properties": {"productName": {"type": "string"}, "fullPrice": {"type": "number"}}}, "CallPlanProductDto": {"required": ["fullPrice", "productName"], "type": "object", "properties": {"productName": {"type": "string"}, "fullPrice": {"type": "number"}}}, "LineRentalProductDto": {"required": ["discountedPrice", "fullPrice", "productName"], "type": "object", "properties": {"contractDuration": {"type": "integer", "format": "int32"}, "productName": {"type": "string"}, "discountedPrice": {"type": "number"}, "fullPrice": {"type": "number"}}}, "OneOffChargeDto": {"required": ["oocPrice", "oocName"], "type": "object", "properties": {"oocName": {"type": "string"}, "oocPrice": {"type": "number"}}}, "PCICSRequestDto": {"required": ["broadbandProduct", "customerType", "journeyContext", "taxTreatment", "totalMonthlyCost"], "type": "object", "properties": {"journeyContext": {"type": "string", "enum": ["SignUp", "Recontract", "Regrade", "HouseMove"]}, "customerType": {"type": "string", "enum": ["Residential"]}, "orderId": {"type": "string"}, "accountId": {"type": "integer", "format": "int32"}, "taxTreatment": {"type": "string", "enum": ["IncVAT", "ExVAT"]}, "broadbandProduct": {"$ref": "#/components/schemas/BroadbandProductDto"}, "lineRentalProduct": {"$ref": "#/components/schemas/LineRentalProductDto"}, "callPlanProduct": {"$ref": "#/components/schemas/CallPlanProductDto"}, "totalOocPrice": {"type": "number"}, "totalCallFeatureBundlePrice": {"type": "number"}, "totalMonthlyCost": {"type": "number"}, "addOns": {"type": "array", "xml": {"wrapped": true}, "items": {"$ref": "#/components/schemas/AddOns"}}, "callFeatures": {"type": "array", "xml": {"wrapped": true}, "items": {"$ref": "#/components/schemas/CallFeatureDto"}}, "oneOffCharges": {"type": "array", "xml": {"wrapped": true}, "items": {"$ref": "#/components/schemas/OneOffChargeDto"}}, "equipmentList": {"type": "array", "xml": {"wrapped": true}, "items": {"type": "string", "xml": {"name": "equipment"}}}}, "xml": {"name": "pcicsrequest", "namespace": "http://www.plus.net/pcicsrequest"}}, "PCICSResponseDto": {"type": "object", "properties": {"orderId": {"type": "string"}, "accountId": {"type": "integer", "format": "int32"}, "streamingId": {"type": "string"}, "streamingUrl": {"type": "string"}, "errorMessage": {"type": "string"}}, "xml": {"name": "pcicsresponse", "namespace": "http://www.plus.net/pcicsresponse"}}}}}