package net.plus.pdfgen.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.apache.commons.codec.digest.DigestUtils;

import java.util.UUID;

@Component
public class Stream
{

    private final String streamingUrl;

    public Stream(@Value("${pcics.stream.url}") String streamingUrl){
        this.streamingUrl = streamingUrl;
    }

    public static String getId()
    {
        UUID uuid = UUID.randomUUID();
        return DigestUtils.md5Hex(uuid.toString().getBytes());
    }

    public String getUrl(String streamingId)
    {
        return this.streamingUrl + "=" + streamingId;
    }

}
