package net.plus.pdfgen.model;

import java.math.BigDecimal;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.xml.bind.annotation.Facets;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import net.plus.pdfgen.constant.CustomerType;
import net.plus.pdfgen.constant.JourneyContext;
import net.plus.pdfgen.constant.TaxTreatment;
import net.plus.pdfgen.deserializer.CurrencyDeserializer;
import net.plus.pdfgen.exception.error.ErrorCode;
import net.plus.pdfgen.model.product.AddOns;
import net.plus.pdfgen.model.product.BroadbandProductDto;
import net.plus.pdfgen.model.product.CallFeatureDto;
import net.plus.pdfgen.model.product.CallPlanProductDto;
import net.plus.pdfgen.model.product.LineRentalProductDto;
import net.plus.pdfgen.util.ValueOfEnum;

@ToString
@Getter
@Setter
@NoArgsConstructor
@XmlRootElement(name = "pcicsrequest",
                namespace = "http://www.plus.net/pcicsrequest")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(namespace = "http://www.plus.net/pcicsrequest")
public class PCICSRequestDto
{
    @XmlElement(required = true)
    @Facets(enumeration = { "SignUp", "Recontract", "Regrade", "HouseMove" })
    @NotNull(message = ErrorCode.MISSING_JOURNEYCONTEXT)
    @ValueOfEnum(enumClass = JourneyContext.class,
                 message = ErrorCode.INVALID_JOURNEYCONTEXT)
    @Valid
    private JourneyContext journeyContext;

    @XmlElement(required = true)
    @Facets(enumeration = { "Residential" })
    @NotNull(message = ErrorCode.MISSING_CUSTOMERTYPE)
    @ValueOfEnum(enumClass = CustomerType.class,
                 message = ErrorCode.INVALID_CUSTOMERTYPE)
    @Valid
    private CustomerType customerType;

    private String orderId;

    private Integer accountId;

    @XmlElement(required = true)
    @Facets(enumeration = { "IncVAT", "ExVAT" })
    @NotNull(message = ErrorCode.MISSING_TAXTREATMENT)
    @ValueOfEnum(enumClass = TaxTreatment.class,
                 message = ErrorCode.INVALID_TAXTREATMENT)
    @Valid
    private TaxTreatment taxTreatment;

    @XmlElement(required = true)
    @NotNull(message = ErrorCode.MISSING_BROADBANDPRODUCT)
    @Valid
    private BroadbandProductDto broadbandProduct;

    @Valid
    private LineRentalProductDto lineRentalProduct;

    @Valid
    private CallPlanProductDto callPlanProduct;

    @XmlElementWrapper(name = "addOns")
    @XmlElement(name = "addOns")
    @JsonProperty("addOns")
    @Valid
    private List<AddOns> addOns;

    @XmlElementWrapper(name = "callFeatures")
    @XmlElement(name = "callFeature")
    @JsonProperty("callFeatures")
    @Valid
    private List<CallFeatureDto> callFeatureList;

    @XmlElementWrapper(name = "oneOffCharges")
    @XmlElement(name = "oneOffCharge")
    @JsonProperty("oneOffCharges")
    @Valid
    private List<OneOffChargeDto> oocList;

    @XmlElementWrapper(name = "equipmentList")
    @XmlElement(name = "equipment")
    @JsonProperty("equipmentList")
    private List<String> equipmentList;

    @JsonDeserialize(using = CurrencyDeserializer.class)
    private BigDecimal totalOocPrice;

    @JsonDeserialize(using = CurrencyDeserializer.class)
    private BigDecimal totalCallFeatureBundlePrice;

    @XmlElement(required = true)
    @NotNull(message = ErrorCode.MISSING_TOTALMONTHLYCOST)
    @JsonDeserialize(using = CurrencyDeserializer.class)
    private BigDecimal totalMonthlyCost;

    public boolean isNewContentToggleOn()
    {
        return isNewContentToggleOn;
    }

    public void setNewContentToggleOn(boolean newContentToggleOn)
    {
        isNewContentToggleOn = newContentToggleOn;
    }

    private boolean isNewContentToggleOn;

}