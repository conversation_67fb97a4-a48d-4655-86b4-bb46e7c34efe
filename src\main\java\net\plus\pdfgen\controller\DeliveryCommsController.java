package net.plus.pdfgen.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

import net.plus.pdfgen.model.ErrorResponseDto;
import net.plus.pdfgen.model.DeliveryCommsDto;
import net.plus.pdfgen.service.DeliveryCommsService;

import javax.validation.Valid;

@RestController
@RequestMapping(path = "/pdfgen/v1/pcics/delivery-comms")
@Slf4j
@Tag(name = "Delivery comms API",
     description = "Manages delivery comms status")
public class DeliveryCommsController
{
    private final DeliveryCommsService deliveryCommsService;

    @Autowired
    public DeliveryCommsController(DeliveryCommsService deliveryCommsService)
    {
        this.deliveryCommsService = deliveryCommsService;
    }

    @PutMapping(path = "/{streamingId}")
    @ResponseStatus(HttpStatus.OK)
    @Operation(tags = "Delivery comms API",
               description = "Update delivery comms status")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "404",
                         description = "Streaming ID not found",
                         content = @Content(
                                 mediaType = "application/json",
                                 schema = @Schema(implementation = ErrorResponseDto.class)
                         )),
            @ApiResponse(responseCode = "400",
                         description = "<ERROR_DESCRIPTION>",
                         content = @Content(
                                 mediaType = "application/json",
                                 schema = @Schema(implementation = ErrorResponseDto.class)
                         )),
            @ApiResponse(responseCode = "500",
                         description = "Internal server error, <ERROR_DESCRIPTION>",
                         content = @Content(
                                 mediaType = "application/json",
                                 schema = @Schema(implementation = ErrorResponseDto.class)
                         )),
            @ApiResponse(responseCode = "200",
                         description = "OK"
            )
    })
    public ResponseEntity updateDeliveryCommsStatus(
            @PathVariable
                    String streamingId,
            @Valid
            @RequestBody
                    DeliveryCommsDto deliveryCommsDto)
    {
        log.info(
                "Entry: DeliveryCommsController.updateDeliveryCommsStatus(), streamingId = {} and deliveryCommsDto = {}",
                streamingId,
                deliveryCommsDto.toString().replaceAll("[\r\n]", ""));
        deliveryCommsService.updateDeliveryCommsStatus(streamingId, deliveryCommsDto);
        log.info("Exit: DeliveryCommsController.updateDeliveryCommsStatus()");
        return new ResponseEntity<>(HttpStatus.OK);
    }
}