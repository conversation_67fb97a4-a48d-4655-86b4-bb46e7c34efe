package net.plus.pdfgen.util;

import lombok.extern.slf4j.Slf4j;

import net.plus.pdfgen.constant.PdfStatus;
import net.plus.pdfgen.dao.PCICSDao;
import net.plus.pdfgen.exception.SharedDirectoryCreationException;
import net.plus.pdfgen.exception.error.ErrorCode;
import net.plus.pdfgen.model.PCICSRequestDto;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.InvalidPathException;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;

@Component
@Slf4j
public class FileUtil
{

    @Value("${pdf.dir.date.format}")
    private String dirDateFormat;

    @Value("${pcics.dir.location}")
    private String baseDirPath;

    private final PCICSDao pcicsDao;

    public FileUtil(PCICSDao pcicsDao)
    {
        this.pcicsDao = pcicsDao;
    }

    public String checkAndCreateDirectory(String streamingId, PCICSRequestDto pcicsRequestDto)
    {

        SimpleDateFormat dateFormat = new SimpleDateFormat(dirDateFormat);
        String dateDirName = dateFormat.format(new Date());
        String dirPath = baseDirPath + dateDirName + "/" + pcicsRequestDto.getCustomerType().value();
        File dir = new File(dirPath);
        if (!dir.exists())
        {

            try
            {
                if (dir.mkdirs())
                {log.info("Created new directory : {}", dirPath);}
            }
            catch (SecurityException e)
            {
                pcicsDao.insertOrUpdatePcicsInfoRecord(pcicsRequestDto,
                                                       streamingId,
                                                       null,
                                                       PdfStatus.PDFGENFAILED.getStatusId());
                throw new SharedDirectoryCreationException(ErrorCode.CREATE_NEW_DIR_FAILURE,
                                                           String.format(
                                                                   "checkAndCreateDirectory : Unable to create new directory : %s ",
                                                                   dirPath),
                                                           streamingId,
                                                           e);
            }
        }
        return dirPath;
    }

    public String getFilePath(String streamingId, PCICSRequestDto pcicsRequestDto)
    {
        String dirPath = checkAndCreateDirectory(streamingId, pcicsRequestDto);
        return dirPath + "/PCI_CS_" + streamingId + ".pdf";
    }

    public boolean validateAndDeleteFile(String streamingId, String fileName)
    {
        try
        {
            Files.delete(Paths.get(fileName));
            log.info("validateAndDeleteFile: Deleted file successfully : [ {} ]", fileName);
            return true;
        }
        catch (IOException | SecurityException | InvalidPathException | NullPointerException e)
        {
            log.error(
                    "StreamingId= [{}] - PDF= [{}] - MESSAGE= [ validateAndDeleteFile: Exception occurred while deleting file ] - EXCEPTION= [{}]",
                    streamingId,
                    fileName,
                    e.fillInStackTrace().toString());
            return false;
        }
    }
}
