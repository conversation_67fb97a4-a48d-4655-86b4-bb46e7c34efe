package net.plus.pdfgen.config;

import javax.sql.DataSource;

import com.zaxxer.hikari.HikariDataSource;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.PropertySource;
import org.springframework.context.annotation.PropertySources;

import lombok.extern.slf4j.Slf4j;

@Configuration
@Slf4j
@PropertySources(value = {
        @PropertySource(value = "classpath:queries.properties",
                        ignoreResourceNotFound = true),
        @PropertySource(value = "classpath:application.properties")
})
public class DbConfig
{

    @Bean(name = "coredbDataSource")
    @Primary
    @ConfigurationProperties(prefix = "coredb-datasource")
    public DataSource coreDataSource()
    {
        log.debug("ENTRY: Creating core DB datasource bean");

        final HikariDataSource dataSource = DataSourceBuilder.create()
                                                             .type(HikariDataSource.class)
                                                             .build();
        dataSource.setPoolName("coredb-pool2");
        dataSource.setRegisterMbeans(true);

        log.debug("EXIT: Created core DB datasource bean");
        return dataSource;
    }

    @Bean(name = "appdbDataSource")
    @ConfigurationProperties(prefix = "appdb-datasource")
    public DataSource appDataSource()
    {
        log.debug("ENTRY: Creating app DB datasource bean");

        final HikariDataSource dataSource = DataSourceBuilder.create()
                                                             .type(HikariDataSource.class)
                                                             .build();
        dataSource.setPoolName("appdb-pool2");
        dataSource.setRegisterMbeans(true);

        log.debug("EXIT: Created app DB datasource bean");
        return dataSource;
    }

}
