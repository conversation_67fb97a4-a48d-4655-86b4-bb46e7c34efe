<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
    <head>
        <meta charset="UTF-8">
        <link th:rel="stylesheet" th:href="@{classpath:static/pcics/css/style.css}">
    </head>

    <body>
        <div align = "center">
            <img class="plusnet_logo" th:src="@{classpath:static/pcics/images/plusnet_logo.pdf}" />
        </div>
        <div>
            <p class="outclass" >
                <b> Pre-contract Information  <span th:text="${pcicsInfo.broadbandProduct.productDisplayName}"></span></b>
            </p>

            <p class="summary1">
                This document is to help you make a comparison between service offers <br> so you can make the right choice for you
            </p>

            <div class="parent">
                <p class ="heading1">
                    <b>1. Identity and contact details of Regulated Provider</b>
                </p>

                <table id="cs_tbl1" class="cs_tbl table-bordered" >
                    <tbody>
                    <tr>
                        <td>Regulated Provider</td>
                        <td>Plusnet plc</td>
                    </tr>
                    <tr>
                        <td>Registered address </td>
                        <td>
                            Plusnet plc<br>Endeavour<br>Sheffield Digital Campus<br>1a Concourse Way<br>Sheffield<br>S1 2BJ.
                        </td>
                    </tr>

                    <tr>
                        <td>For complaints </td>
                        <td>For complaints about home phone and broadband, please call us on 0800 432 0200.
                            <br><br>
                            If you'd prefer to contact us by an online ticket please log in to My Account and we'll respond within 5 working days, <a class="a_margin" href="https://www.plus.net/help/assistant">https://www.plus.net/help/assistant</a>.
                            <br><br>
                            If you want to write to us, we'll reply to your letter within 10 working days.</td>
                    </tr>

                    </tbody>
                </table>
                <hr class="hr"/>
                <p class ="heading1">
                    <b>2. Description of services</b>
                </p>

                <table id="cs_tbl2" class="cs_tbl table-bordered">
                    <thead>
                    <tr>
                        <td colspan="2" class="heading3">Package Summary</td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td><b>Package Name</b></td>
                        <td th:text="${pcicsInfo.broadbandProduct.productDisplayName}" />
                    </tr>
                    <tr th:unless="${#lists.isEmpty(pcicsInfo.addOns) }">
                        <td><b>Add-Ons</b></td>
                        <td th:text="${#strings.listJoin(#lists.toList(pcicsInfo.addOns.{c2mProductName == 'PlusnetProtect' ? 'Access to Plusnet Protect powered by Norton' : productDisplayName}), ', ')}"/>
                    </tr>
                    <tr th:unless="${pcicsInfo.lineRentalProduct == null}">
                        <td><b>Line Rental</b> </td>
                        <td th:text="${pcicsInfo.lineRentalProduct.productDisplayName}" />
                    </tr>
                    <tr th:unless="${pcicsInfo.callPlanProduct == null}">
                        <td><b>Call Plan</b> </td>
                        <td th:text="${pcicsInfo.callPlanProduct.productDisplayName}" />
                    </tr>
                    <th:block th:unless="${#lists.isEmpty(pcicsInfo.callFeatureList)}">
                        <tr>
                            <td><b>Call Features</b></td>
                            <td/>
                        </tr>
                        <tr th:each="callFeature : ${pcicsInfo.callFeatureList}">
                            <td th:text="${callFeature.productDisplayName}"/>
                            <td th:text="${callFeature.productDescription}"/>
                        </tr>
                    </th:block>
                    <tr>
                        <td colspan="2">
                            If your package includes a reward card we'll send you information on how to claim this separately.
                        </td>
                    </tr>
                    </tbody>
                </table>
                <br/>
                <th:block  th:unless="${#lists.isEmpty(pcicsInfo.equipmentList)}">
                    <table id="cs_tbl3" class="cs_tbl table-bordered">
                        <thead>
                        <tr>
                            <td colspan="2" class="heading3">Equipment</td>
                        </tr>
                        </thead>
                        <colgroup>
                            <col span="1" style="width: 33%;"/>
                        </colgroup>
                        <tbody>
                        <tr th:unless="${#lists.isEmpty(pcicsInfo.equipmentList) }">
                            <td>Plusnet Broadband Equipment</td>
                            <td>
                                <th:block th:each="equipment : ${pcicsInfo.equipmentList}">
                                    <span th:text="${equipment}"/> <br>
                                </th:block>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </th:block>
                <br/>
                <table id="cs_tbl3" class="cs_tbl table-bordered">
                    <tr>
                        <td colspan="2">
                            <span>So that your services are stable and operate efficiently, we may take action to manage the network's performance during periods where there is a high demand and prioritise particular types of Internet traffic.</span>
                        </td>
                    </tr>
                </table>
                <br>
                <hr class="hr"/>
                <p class ="heading1">
                    <b>3. Service Characteristics </b>
                </p>

                <table id="cs_tbl5" class="cs_tbl table-bordered" >
                    <thead>
                    <tr>
                        <td colspan="2" class="heading3"> <b>Broadband Speeds</b></td>
                    </tr>
                    </thead>
                    <colgroup>
                        <col span="1" style="width: 39%;"/>
                    </colgroup>
                    <tbody>
                        <tr th:if="${!#strings.isEmpty(pcicsInfo.broadbandProduct.estimatedDownloadSpeedMin)
                            && !#strings.isEmpty(pcicsInfo.broadbandProduct.estimatedDownloadSpeedMax)}">
                            <td>Estimated Download Speed Range</td>
                            <td th:text="${pcicsInfo.broadbandProduct.estimatedDownloadSpeedMin}
                            + ' &ndash; '
                            + ${pcicsInfo.broadbandProduct.estimatedDownloadSpeedMax}"/>
                        </tr>
                        <tr th:if="${!#strings.isEmpty(pcicsInfo.broadbandProduct.estimatedUploadSpeedMin)
                            && !#strings.isEmpty(pcicsInfo.broadbandProduct.estimatedUploadSpeedMax)}">
                            <td>Estimated Upload Speed Range</td>
                            <td th:text="${pcicsInfo.broadbandProduct.estimatedUploadSpeedMin}
                            + ' &ndash; '
                            + ${pcicsInfo.broadbandProduct.estimatedUploadSpeedMax}"/>
                        </tr>
                        <tr th:if="${!#strings.isEmpty(pcicsInfo.broadbandProduct.minGuaranteedSpeed)}">
                            <td>Minimum Guaranteed Speed (Download)</td>
                            <td th:text="${pcicsInfo.broadbandProduct.minGuaranteedSpeed}"/>
                        </tr>
                        <tr>
                            <td>Technology Type</td>
                            <td>
                                <div th:if="${pcicsInfo.broadbandProduct.displayName != null}">
                                    <span th:text="${pcicsInfo.broadbandProduct.getDisplayName()}"></span>
                                    <br><br>
                                </div>
                                To find out more about your broadband technology, please visit <a class="a_margin" href="https://www.plus.net/help/broadband/broadband-package-guide/">https://www.plus.net/help/broadband/broadband-package-guide/</a>
                            </td>
                        </tr>
                        <tr>
                            <td>Minimum Guaranteed Speed</td>
                            <td>
                                When you choose your broadband service with us we'll give you a speed estimate in the form of a range, in Megabits per second (Mbps). Under normal circumstances the speed you get should fall within this range.
                                <br><br>
                                We'll also give you a Minimum Guaranteed Speed. If your speed consistently falls below this value you should contact us. We'll try to fix the issue and aim to get your speed above the minimum we gave you, and if possible, within your original speed estimate range.
                                <br><br>
                                If we can't get this resolved within a minimum period of 30 days you should contact us. You'll be given the opportunity to leave your contract without an early termination charge.
                                <br><br>
                                For more information please see <a href="https://www.plus.net/help/legal/ofcom-voluntary-speed-code-of-practice/">https://www.plus.net/help/legal/ofcom-voluntary-speed-code-of-practice/</a>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="2">
                                <span>Our broadband packages don't have any speed restrictions or restrict your usage or downloads. So, if you're on one of our packages, you don't need to worry about us slowing you down even at peak times. We won't slow down peer-to-peer file sharing either. More information is available here <a href="https://www.plus.net/help/broadband/about-traffic-prioritisation/">https://www.plus.net/help/broadband/about-traffic-prioritisation/</a>
                                    <br><br>
                                    Just so you know, the speed ranges we show are estimates. Your actual speed depends on your location, line, home wiring, Wi-fi connection and time of day. It can vary throughout the day and can be slower during busy periods, especially between 8 - 10pm. This is the equivalent of internet rush hour when lots of people are using the shared parts across the network at the same time. So faster broadband speeds means faster downloads of content such as music or films.</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <hr class="hr"/>

                <p class ="heading1">
                    <b>4. Price</b>
                </p>

                <table id="cs_tbl6" class="cs_tbl table-bordered" >
                    <thead>
                    <tr>
                        <td colspan="2" class="heading3"> <b>Monthly costs</b></td>
                    </tr>
                    </thead>
                    <colgroup>
                        <col span="1" style="width: 38%;"/>
                    </colgroup>
                    <tbody>
                    <tr>
                        <td><b>Broadband Package</b></td>
                        <td>
                            <p class="summary2">
                            <div>
                                <table class="custom-table">
                                    <tr>
                                        <td th:text="${pcicsInfo.broadbandProduct.productDisplayName}"></td>
                                        <td th:text="'&pound;' + ${pcicsInfo.broadbandProduct.discountedPrice} + ' a month'"></td>
                                    </tr>
                                    <!-- Check if futureDate1 and futurePrice1 are available -->
                                    <tr th:if="${pcicsInfo.broadbandProduct.futureDate1 != null && pcicsInfo.broadbandProduct.futurePrice1 != null}">
                                        <td th:text="'From ' + ${#dates.format(pcicsInfo.broadbandProduct.getFutureDate1AsDate(), 'dd MMMM yyyy')}"></td>
                                        <td th:text="'&pound;' + ${pcicsInfo.broadbandProduct.futurePrice1} + ' a month'"></td>
                                    </tr>
                                    <!-- Check if futureDate2 and futurePrice2 are available -->
                                    <tr th:if="${pcicsInfo.broadbandProduct.futureDate2 != null && pcicsInfo.broadbandProduct.futurePrice2 != null}">
                                        <td th:text="'From ' + ${#dates.format(pcicsInfo.broadbandProduct.getFutureDate2AsDate(), 'dd MMMM yyyy')}"></td>
                                        <td th:text="'&pound;' + ${pcicsInfo.broadbandProduct.futurePrice2} + ' a month'"></td>
                                    </tr>
                                </table>
                                <span>At the end of your minimum term your price will increase and we will write to you in advance to tell you what the new price will be. Our standard prices are set out in our </span>
                                <a href="https://www.plus.net/help/legal/plusnet-price-guide-for-residential-products/">price guide</a>
                            </div>
                            </p>
                        </td>
                    </tr>
                    <tr th:unless="${#lists.isEmpty(pcicsInfo.addOns) }">
                        <td><b>Add-Ons</b></td>
                        <td>
                            <span th:each="addOn, addOnStat : ${pcicsInfo.addOns}">
                                <span th:if="${addOnStat.last}">
                                    <span th:if="${addOn.c2mProductName == 'PlusnetProtect'}"
                                          th:text="'Access to Plusnet Protect powered by Norton, &pound;' + ${addOn.fullPrice} + ' a month for 2 years from activation'"></span>
                                    <span th:if="${addOn.c2mProductName != 'PlusnetProtect'}"
                                          th:text="${addOn.productDisplayName} + ' &pound;' + ${addOn.fullPrice} + ' a month'"></span>
                                </span>
                                <span th:if="${!addOnStat.last}">
                                    <span th:if="${addOn.c2mProductName == 'PlusnetProtect'}"
                                          th:text="'Access to Plusnet Protect powered by Norton, &pound;' + ${addOn.fullPrice} + ' a month for 2 years from activation, '"></span>
                                    <span th:if="${addOn.c2mProductName != 'PlusnetProtect'}"
                                          th:text="${addOn.productDisplayName} + ' &pound;' + ${addOn.fullPrice} + ' a month, '"></span>
                                </span>
                            </span>
                        </td>
                    </tr>

                    <tr th:unless="${pcicsInfo.lineRentalProduct == null}">
                        <td><b>Line Rental</b></td>
                        <td>
                            <p class="summary2">
                            <span th:text="${pcicsInfo.lineRentalProduct.productDisplayName}
                                    + ' &pound;'
                                    + ${pcicsInfo.lineRentalProduct.discountedPrice}
                                    + ' a month. At the end of your minimum commitment period your price will increase
                                       and we will write to you in advance to tell you what the new price will be.
                                       Our standard prices are set out in our '"/>
                            <a href="https://www.plus.net/help/legal/plusnet-price-guide-for-residential-products/">price
                                guide</a>
                            </p>
                        </td>
                    </tr>
                    <tr th:unless="${pcicsInfo.callPlanProduct == null}">
                        <td><b>Call Plan</b></td>
                        <td th:text="${pcicsInfo.callPlanProduct.productDisplayName}
                            + ' &pound;'
                            + ${pcicsInfo.callPlanProduct.fullPrice}
                            + ' a month'" />
                    </tr>
                    <th:block th:unless="${#lists.isEmpty(pcicsInfo.callFeatureList) }">
                        <tr>
                            <td><b>Call Features</b></td>
                            <td></td>
                        </tr>
                        <tr th:each="callFeature : ${pcicsInfo.callFeatureList}" th:if="${callFeature.fullPrice > 0}">
                            <td th:text="${callFeature.productDisplayName}"/>
                            <td th:text="'&pound;'
                            + ${callFeature.fullPrice}
                            + ' a month (not available in feature bundles).'"/>
                        </tr>
                    </th:block>
                    <tr th:if="${!#lists.isEmpty(pcicsInfo.callFeatureList)
                        && (#lists.contains(#numbers.listFormatCurrency(#lists.toList(pcicsInfo.callFeatureList.{fullPrice})), '$0.00')
                            || #lists.contains(#numbers.listFormatCurrency(#lists.toList(pcicsInfo.callFeatureList.{fullPrice})), '&pound;0.00'))}"
                    >
                        <td>You've taken the following features as part of a feature bundle (free of charge features also shown)</td>
                        <td>
                            <th:block th:each="callFeature : ${pcicsInfo.callFeatureList}" th:if="${!callFeature.fullPrice > 0}">
                                <span th:text="${callFeature.productDisplayName}"/>
                                <br/>
                            </th:block>
                        </td>
                    </tr>
                    <tr th:if="${pcicsInfo.totalCallFeatureBundlePrice != null}">
                        <td><b>Monthly cost for feature bundle</b></td>
                        <td th:text="'&pound;'
                            + ${pcicsInfo.totalCallFeatureBundlePrice}"/>
                    </tr>
                    <tr>
                        <td><b>Total Monthly Costs</b></td>
                        <td th:text="'&pound;'
                            + ${pcicsInfo.totalMonthlyCost}" />
                    </tr>
                    <tr>
                        <td colspan="2">
                            <span>* If you're due a discount, you'll see this on your bill</span>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <br>
                <th:block  th:unless="${#lists.isEmpty(pcicsInfo.oocList)}">
                    <table id="cs_tbl71" class="cs_tbl table-bordered">
                        <thead>
                        <tr>
                            <td colspan="2" class="heading3"><b>One-off charges</b></td>
                        </tr>
                        </thead>
                        <colgroup>
                            <col span="1" style="width: 38%;"/>
                        </colgroup>
                        <tbody>

                        <tr th:each="oneOffCharge : ${pcicsInfo.oocList}" th:if="${oneOffCharge.oocPrice > 0}">
                            <td th:text="${oneOffCharge.oocName}" />
                            <td th:text="'&pound;'
                                + ${oneOffCharge.oocPrice}" />
                        </tr>
                        <tr>
                            <td>Total One-Off Charges</td>
                            <td th:text="'&pound;'
                                + ${pcicsInfo.totalOocPrice}" />
                            <!--#aggregates.sum(pcicsInfo.oocList.{fullPrice})}"/>-->
                        </tr>
                        </tbody>
                    </table>
                    <br>
                </th:block>
                <p class="summary2">
                    The monthly price for broadband will increase by <td th:text="'&pound;'+ 3" /> on 31 March each year.  All out of bundle charges will be increased by 5% (rounded to the nearest whole pence).
                </p>

                <p class="summary2">
                    If you have a phone package as part of your service with us, and you are not moving to a new product, then we'll also increase the cost of international calls (calls from your landline to other countries) when our third-party partners put the costs up for us.
                    <br><br>
                    We may also increase any charges at any time. If we do, you may be able to end the service early without paying a cancellation charge.
                    <br><br>
                    See our terms and conditions <a href="https://www.plus.net/help/legal/">https://www.plus.net/help/legal/</a> and <a class="a_margin" href="https://www.plus.net/help/legal/about-annual-price-changes/">https://www.plus.net/help/legal/about-annual-price-changes/</a>
                </p>

                <p class="summary2">
                    For details of how to monitor and manage your usage see <a href="https://www.plus.net/help/my-account/your-broadband-bill-explained/">https://www.plus.net/help/my-account/your-broadband-bill-explained/</a>
                </p>
                <hr class="hr"/>
                <p class ="heading2">
                    <b>5. Delivery of service </b>
                </p>
                <p class="summary2">
                    For information on the right to cancel see section <b>10</b> of the Pre-Contract Information document.
                </p>

                <table id="cs_tbl7" class="cs_tbl table-bordered" >
                    <colgroup>
                        <col span="1" style="width: 33%;"/>
                    </colgroup>
                    <tbody>
                    <tr>
                        <td>Payment Methods</td>
                        <td>You can pay by Direct Debit, credit card or debit card.<br>More information about making payment for the service is available here <a href="https://www.plus.net/help/legal/failed-payments/">https://www.plus.net/help/legal/failed-payments/</a></td>
                    </tr>

                    <tr>
                        <td colspan="2">
                            <span> If you set up direct debit your payment is in advance by monthly direct debit. We'll take payment from your registered bank account using the details you provided. Your recurring payment date will be confirmed in your order confirmation. Activation fee is paid up front by debit/credit card. For more information see <a href="https://www.plus.net/help/my-account/paying-your-bill/">https://www.plus.net/help/my-account/paying-your-bill/</a></span>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <p class ="heading3">
                    <b>Broadband Provisioning date</b>
                </p>
                <p class="summary2" th:if="${#strings.equals(pcicsInfo.journeyContext, 'SignUp')}">
                    If you're new to Plusnet, we'll aim to get your broadband up and running within 2 weeks, although this can sometimes take longer.
                </p>
                <p class="summary2" th:if="${#strings.equals(pcicsInfo.journeyContext, 'Recontract')
                    or #strings.equals(pcicsInfo.journeyContext, 'Regrade')
                    or #strings.equals(pcicsInfo.journeyContext, 'HouseMove')}">
                    &bull; If you're moving home it may take up to 2 weeks &ndash; occasionally longer &ndash; so please contact us when you know the date.
                    <br>
                    &bull; If you're staying at the same address on the same product (e.g. Unlimited Broadband, Unlimited Fibre or Unlimited Fibre Extra) this is done on the same day.
                    <br>
                    &bull; If you're staying at the same address and changing product (e.g. Unlimited Broadband, Unlimited Fibre or Unlimited Fibre Extra) it should be 1 &ndash; 2 weeks.
                    <br>
                    &bull; On the day we'll switch it on anytime up to midnight and well do our best to ensure you don't lose service.
                    <br>
                    &bull; You can check your activation date and follow the status of your broadband activations by tracking your order online.
                </p>
                <hr class="hr"/>
                <p class ="heading1">
                    <b>6. Switching to Plusnet </b>
                </p>
                <p class="heading3">
                    <b>Lead Times</b>
                </p>
                <p class="summary2"><b>If you're new to Plusnet</b>
                    <br>
                    <br>
                    Move your broadband in 3 simple steps:
                    <br>
                    1.	Choose one of our great value broadband packages. No need to contact your current provider.
                    <br>
                    2.	We'll give you a switching date - normally within 2 weeks, unless you choose a later one. Your new Plusnet Hub will arrive by post. It's specially designed to fit through most letter boxes.
                    <br>
                    3.	On the day, We'll switch it on anytime up to midnight and we'll do our best to ensure you don't lose service. We'll text you when your Plusnet Broadband is activated. Then you just connect your Plusnet Hub yourself.
                </p>

                <p class="summary2"><b>If you're an existing customer and staying on the same product</b>
                    <br>
                    <br>
                    If you're recontracting and staying at the same address and on the same product this is usually completed on the same day.
                </p>

                <p class="summary2"><b>If you're an existing customer moving to a new product</b>
                    <br>
                    <br>
                    The lead times for these changes can vary depending on the products involved.
                    <br>
                    <br>
                    We'll arrange the orders and give you a switching date - normally within 2 weeks, unless you choose a later one. If your change includes a new Plusnet Hub, it will arrive by post. It's specially designed to fit through most letter boxes.
                    <br>
                    <br>
                    Under our Automatic Compensation Scheme, you will be entitled to compensation if switching to Plusnet and we do not activate your service on the day we promised. For more details, please refer to <a href="https://www.plus.net/help/legal/automaticcompensation/">https://www.plus.net/help/legal/automaticcompensation/</a>.
                    <br>
                    <br>
                    For more information on switching to Plusnet, visit <a href="https://www.plus.net/broadband/switch/">https://www.plus.net/broadband/switch/</a>.
                </p>

                <hr class="hr"/>
                <p class ="heading1">
                    <b>7. Duration, renewal and termination of contract </b>
                </p>
                <p class="heading3">
                    <b>Duration:</b>
                </p>
                <table id="cs_tbl8" class="cs_tbl table-bordered">
                    <colgroup>
                        <col span="1" style="width: 50%;"/>
                    </colgroup>
                    <tr>
                        <td><b>Product</b></td>
                        <td><b>Contract Length</b></td>
                    </tr>
                    <tbody>

                    <tr th:if="${pcicsInfo.broadbandProduct.contractDuration != null}">
                        <td>Broadband Package Name:<br>
                            <span th:text="${pcicsInfo.broadbandProduct.productDisplayName}"/>
                        </td>
                        <td th:text="${pcicsInfo.broadbandProduct.contractDuration}
                            + ' month Minimum Term'" />
                    </tr>
                    <tr th:unless="${#lists.isEmpty(pcicsInfo.addOns) }">
                        <td>Add-Ons</td>
                        <td>30 day Minimum Term</th:block>
                        </td>
                    </tr>
                    <tr th:if="${pcicsInfo.lineRentalProduct != null && pcicsInfo.lineRentalProduct.contractDuration != null}">
                        <td>Line Rental</td>
                        <td th:text="${pcicsInfo.lineRentalProduct.contractDuration}
                            + ' month Minimum Term'"/>
                    </tr>
                    <tr th:if="${pcicsInfo.callPlanProduct != null}">
                        <td>Call Plan</td>
                        <td>30 day Minimum Term</td>
                    </tr>
                    <tr th:unless="${#lists.isEmpty(pcicsInfo.callFeatureList) }">
                        <td>Call Features</td>
                        <td>30 day  Minimum Term</td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <span>For most services, the minimum term starts when the service starts which is outlined in the terms and conditions. </span>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <p class="heading3">
                    <b>Renewal:</b>
                </p>
                <p class="summary2">
                    Each service will last for a minimum amount of time (the minimum term), which is set out above. Once that ends, each service will continue until cancelled in accordance with the terms and conditions <a class="a_margin" href="https://www.plus.net/help/legal/">https://www.plus.net/help/legal/</a>.
                    <br><br>
                    If you have other contracts with us their minimum terms may not start or end at the same time as the minimum term for this contract.
                </p>
                <p class="summary2" th:if="${pcicsInfo.broadbandProduct.c2mProductName != 'UnlimitedFibre' and pcicsInfo.broadbandProduct.c2mProductName != 'FullFibre36'}">
                    If you choose to activate Plusnet Protect powered by Norton, you will need to register directly as a customer with Norton in order to download the software. Any renewal of this service beyond the initial 2-year free period will be negotiated directly with Norton and does not affect your contract with Plusnet.
                </p>
                <p class="heading3">
                    <b>Termination:</b>
                </p>
                <p class="summary2">
                    When you want to end your service you generally have two options:
                    <br>
                    &bull; to completely stop receiving the service from us or any other provider (we call this ceasing your service); or
                    <br>
                    &bull; to just stop receiving the service from us and to move to another provider.
                    <br><br>
                    When speaking to us about ending your service please be clear on whether you're ceasing your service or moving to a new provider. This will prevent any confusion and your service being ceased unnecessarily (with the cost of a new provider re-activating the line and the hassle of the associated service downtime).
                    <br><br>
                    You can end each service using one of the ways set out in our terms and conditions. You'll need to contact us and give us 14 days' notice unless you're switching to another provider through an approved switching process, your agreement will end on the date your switch completes. Our website sets out details of how you may contact us to cancel. See our terms and conditions <a href="https://www.plus.net/help/legal/">https://www.plus.net/help/legal/</a> for more details.
                    <br><br>
                    You may have to pay us early termination charges if you end a service during the minimum term in accordance with our terms and conditions. See our terms and conditions and Price Guide <a href="https://www.plus.net/help/legal/plusnet-price-guide-for-residential-products/">https://www.plus.net/help/legal/plusnet-price-guide-for-residential-products/</a> for more information.
                    <br><br>
                    You may not need to pay early termination charges to end a service during the minimum term if we've made a change to it. See our terms and conditions for more information.
                    <br><br>
                    In some situations, this contract may form a linked contract with other contracts you take from us. If that's the case, in some situations where you're able to cancel a linked contract without having to pay early termination charges to leave early, you may also be able to cancel this contract. See our terms and conditions for more information.
                </p>
                <hr class="hr"/>
                <p class ="heading2">
                    <b>8. Security</b>
                </p>
                <p class="summary2">
                    If we suspect there's been, or is likely to be, a security incident, we may suspend your Plusnet username to protect your account. We'll ask you to change your password before letting you log back in.
                    <br><br>
                    More information about staying safe online can be found at <a href="https://www.plus.net/help/broadband/stay-safe-online/">https://www.plus.net/help/broadband/stay-safe-online/</a>

                </p>
                <hr class="hr"/>
                <p class ="heading2">
                    <b>9. Remedies, complaints handling and dispute resolution</b>
                </p>
                <p class="summary2">
                    If you have issues with your service, please contact us to resolve. Where you have speed issues, in certain circumstances you'll be able to cancel your service early without paying a fee should speeds consistently fall short of the Minimum Guaranteed Speed. You also have other legal options, see <a class="a_margin" href="https://www.citizensadvice.org.uk/">https://www.citizensadvice.org.uk/</a>. Please also note that we will comply with all relevant Ofcom General Conditions and Voluntary Codes of Practice where we have agreed to that particular code. See our terms and conditions <a href="https://www.plus.net/help/legal/">https://www.plus.net/help/legal/</a> and <a href="https://www.plus.net/help/legal/ofcom-voluntary-speed-code-of-practice/">https://www.plus.net/help/legal/ofcom-voluntary-speed-code-of-practice/</a> for more information.
                    <br><br>
                    Our Code of Practice, see <a href="https://www.plus.net/help/legal/plusnet-code-of-practice/">https://www.plus.net/help/legal/plusnet-code-of-practice/</a> and our Customer Complaint Code, see <a href="https://www.plus.net/help/legal/complaints-code-of-practice/">https://www.plus.net/help/legal/complaints-code-of-practice/</a> tell you how to contact us to sort out a problem and how we'll deal with any complaint or dispute. You may be able to refer the matter to an alternative dispute resolution service to get an independent opinion. More details are given in our Code of Practice, Customer Complaints Code and our terms and conditions, see <a class="a_margin" href="https://www.plus.net/help/legal/">https://www.plus.net/help/legal/</a>.
                </p>
                <hr class="hr"/>
                <p class ="heading2">
                    <b>10. Right to cancel</b>
                </p>
                <p class="summary2">
                    You have a 14-day cooling off period in case you change your mind and want to cancel your service. If you cancel during this cooling off period, you'll have to pay for the services you have used, any applicable installation, connection or activation charges (including the full cost of any charges that were discounted or advertised as free as a condition of taking services on the terms that you agreed) and return any equipment provided. You must return equipment within 14 days of telling us you want to cancel at your own cost and, if you don't, you'll have to pay the full price of the equipment. If you return the equipment to us later, we'll waive or return what you have been charged for the equipment.
                    <br><br>
                    As Plusnet Protect is digital content which is available immediately, once you start downloading content you'll lose the 14 day cooling off period to change your mind for these.
                    <br><br>
                    Please see our terms and conditions <a href="https://www.plus.net/help/legal/">https://www.plus.net/help/legal/</a> for more details.
                </p>
                <hr class="hr"/>
                <p class ="heading2">
                    <b>11. Equipment</b>
                </p>
                <p class="summary2">
                    We may loan you equipment as part of your service (as shown in your order confirmation). We'll tell you when you should get the loaned equipment. You may also be able to get other equipment from us at a cost.
                    <br>
                    If there are any charges for equipment, we'll tell you before you order.
                    <br><br>
                    If you end a service, you must return any loaned equipment and, if you don't return it within 60 days of ending the service, you'll be charged. Please see our terms and conditions <a href="https://www.plus.net/help/legal/">https://www.plus.net/help/legal/</a> for more information.
                    <br><br>
                    Any loaned equipment (except the software in it) and any Plusnet-owned equipment belongs to us at all times. You must look after it and not dispose, damage, destroy or otherwise interfere with it unless we ask you to. If the loaned equipment is damaged other than through fair wear and tear, you'll have to pay to fix or replace it. Please see our terms and conditions <a href="https://www.plus.net/help/legal/">https://www.plus.net/help/legal/</a> for more information.
                </p>
                <hr class="hr"/>
                <p class ="heading2">
                    <b>12. Data protection</b>
                </p>
                <p class="summary2">
                    We need to collect and use personal information so we can set you up and provide you with our products or services. This includes when you register, buy or use one of our products or services, if you express an interest in our products and services, and if you download and register on one of our apps. If you don't provide us with the correct information, we may be unable to provide you with the product or service.
                    <br><br>
                    The type of personal information we may need includes your contact details and other information to confirm your identity and communications with us, such as your name, gender, address, phone number, date of birth, email address and a security question and answer. If you choose to buy something from us we'll need your payment and financial information too.  We also collect and use certain personal information when you use our services, such as your IP address and other online identifiers.
                    <br><br>
                    If you tell us you have a disability or otherwise need support, we'll note that you are a vulnerable customer, but only if you give your permission or if we must for legal or regulatory reasons.

                </p>
                <th:block th:if="${pcicsInfo.callPlanProduct != null}">
                    <p class="heading3">
                        <b>Plusnet Home Phone</b>
                    </p>
                    <p class="summary2">
                        We must keep a directory of numbers for regulatory reasons. If you order a phone service we'll ask how you want to be listed in our public directory such as the phone book. If you are happy to be listed in our public directory we'll publish your details and share that information with other providers. Ex-directory numbers aren't included in our public directory and won't appear. You can request to change your public directory at any time by contacting one of our advisors.
                    </p>
                </th:block>
                <p class="summary2">
                    Further information can be found in our privacy policy which can be found on our website at <a href="https://www.plus.net/help/legal/privacy-policy/" >https://www.plus.net/help/legal/privacy-policy/</a>
                </p>
                <hr class="hr"/>
                <p class ="heading2">
                    <b>13. Customers with disabilities</b>
                </p>
                <p class="summary2">
                    We offer a range of services to help customers with disabilities and vulnerabilities stay connected, manage their account and communicate with us as easily as possible. See <a href="https://www.plus.net/help/legal/support-for-customers-with-disabilities/">https://www.plus.net/help/legal/support-for-customers-with-disabilities/</a>
                </p>
                <hr class="hr"/>
                <p class ="heading2">
                    <b>14.	Accessibility</b>
                </p>
                <p class="summary2">
                    If you are using a VOIP, emergency services will not be able to automatically access your caller location. To avoid this problem you should register, with Plusnet, the location where the outbound call service is to be used and update this information if the location changes. If you do not register, and need to make an emergency call, you will need to provide your location.
                </p>
                <br>
            </div>
        </div>
    </body>
</html>