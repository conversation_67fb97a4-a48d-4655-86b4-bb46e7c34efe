CREATE SCHEMA pdfgen;

create table pdfgen.PDFGENSTATUS
(
    STATUS_ID TINYINT           not null,
    STATUS    CHARACTER VARYING not null,
    constraint PDFGENSTATUS_PK
        primary key (STATUS_ID)
);

create table pdfgen.PCICS_INFO
(
    STREAMING_ID     CHARACTER VARYING                   not null,
    ACCOUNT_ID       INTEGER,
    ORDER_ID         CHARACTER VARYING,
    CUSTOMER_TYPE    CHARACTER VARYING,
    FILE_PATH        CHARACTER VARYING,
    LAST_UPDATED_DTM TIMESTAMP default CURRENT_TIMESTAMP not null,
    CREATED_DTM      TIMESTAMP                           not null,
    STATUS_ID        TINYINT                             not null,
    constraint PCICS_INFO_PK
        primary key (STREAMING_ID),
    constraint PCICS_INFO_PDFGENSTATUS_STATUS_ID_FK
        foreign key (STATUS_ID) references pdfgen.PDFGENSTATUS(STATUS_ID)
);

create table pdfgen.DELIVERY_INFO
(
    STREAMING_ID     CHARACTER VARYING(32)               not null,
    ORDER_ID         CHARACTER VARYING(40),
    DELIVERY_TYPE    CHARACTER VARYING(20)               not null,
    HANDLE           CHARACTER VARYING(40),
    LAST_UPDATED_DTM TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    CREATED_DTM      TIMESTAMP                           not null,
    STATUS           CHARACTER VARYING(10)               not null,
    primary key (STREAMING_ID, DELIVERY_TYPE)
);


INSERT INTO pdfgen.pdfgenstatus
(status_id, status)
VALUES (1, 'INITIATED') ,
       (2, 'COMPLETED') ,
       (3, 'DELETED') ,
       (4, 'PDFGENFAILED');

CREATE SCHEMA dbAdminTools;

CREATE TABLE dbAdminTools.tblFeatureToggle
(
    featureToggleId INTEGER NOT NULL AUTO_INCREMENT,
    toggleName      CHARACTER VARYING(64) not null UNIQUE,
    onDateTime      timestamp,
    offDateTime     timestamp,
    audience        TINYINT NOT NULL DEFAULT 0,
    techDebtUrl     CHARACTER VARYING(128) NOT NULL,
    createdBy       CHARACTER VARYING(32)  NOT NULL,
    stmLastUpdate   timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (featureToggleId)
);

INSERT INTO dbAdminTools.tblFeatureToggle
(toggleName, onDatetime, offDatetime, audience, techDebtUrl, createdBy)
--VALUES ('PCICS_Content_Toggle', '2024-09-12', NULL, 1, 'https://jira.int.plus.net/browse/BD-7219', 'emathew');
VALUES ('PCICS_Content_Toggle', current_date, NULL, 1, 'https://jira.int.plus.net/browse/BD-7219', 'emathew');--for testing

--GRANT SELECT ON dbAdminTools.tblFeatureToggle TO 'pdfgenuser'@'%';

set mode MySQL;

COMMIT;
