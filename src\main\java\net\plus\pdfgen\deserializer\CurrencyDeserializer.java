package net.plus.pdfgen.deserializer;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.deser.std.NumberDeserializers;

public class CurrencyDeserializer
        extends NumberDeserializers.BigDecimalDeserializer {

    @Override
    public BigDecimal deserialize(JsonParser p, DeserializationContext ctxt) throws IOException
    {
        BigDecimal value = super.deserialize(p, ctxt);
        // set scale
        value = value.setScale(2);
        return value;
    }
}