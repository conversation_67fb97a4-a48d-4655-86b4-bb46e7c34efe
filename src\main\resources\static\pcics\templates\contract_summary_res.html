<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
    <head>
        <meta charset="UTF-8">
        <link th:rel="stylesheet" th:href="@{classpath:static/pcics/css/style.css}">
    </head>

    <body>
        <div align="center">
            <img class="plusnet_logo" th:src="@{classpath:static/pcics/images/plusnet_logo.pdf}" />
        </div>
        <div class="date-top-right">
            <p th:text="${currentDate}"></p>
        </div>
        <div>
            <p class="outclass" >
                <b> Contract Summary <span th:text="${pcicsInfo.broadbandProduct.productDisplayName}"/></b>
            </p>
            <p class="summary1">
                This contract summary provides the main elements of this service offer. It helps to make a comparison between service offers.<br/>
                Complete information about the service is provided in other documents.
            </p>

            <div class="parent">
                <p class ="heading1">

                    <b>1. Services</b>
                </p>

                <table id="cs_tbl1" class="cs_tbl table-bordered" >
                    <thead>
                    <tr>
                        <td colspan="2"  class="heading3"> <b>Package Summary</b></td>
                    </tr>

                    </thead>
                    <tbody>

                    <tr>
                        <td><b>Package Name</b></td>
                        <td><span th:text="${pcicsInfo.broadbandProduct.productDisplayName}" /></td>
                    </tr>
                    <tr th:unless="${#lists.isEmpty(pcicsInfo.addOns) }">
                        <td><b>Add-Ons</b></td>
                        <td th:text="${#strings.listJoin(#lists.toList(pcicsInfo.addOns.{c2mProductName == 'PlusnetProtect' ? 'Access to Plusnet Protect powered by Norton' : productDisplayName}), ', ')}"/>
                    </tr>

                    <tr th:unless="${pcicsInfo.lineRentalProduct == null}">
                        <td><b>Line Rental</b> </td>
                        <td><span th:text="${pcicsInfo.lineRentalProduct.productDisplayName}" /></td>
                    </tr>
                    <tr th:unless="${pcicsInfo.callPlanProduct == null}">
                        <td><b>Call Plan</b> </td>
                        <td><span th:text="${pcicsInfo.callPlanProduct.productDisplayName}" /></td>
                    </tr>
                    <th:block th:unless="${#lists.isEmpty(pcicsInfo.callFeatureList)}">
                        <tr>
                            <td><b>Call Features</b></td>
                            <td/>
                        </tr>
                        <tr th:each="callFeature : ${pcicsInfo.callFeatureList}">
                            <td th:text="${callFeature.productDisplayName}"/>
                            <td th:text="${callFeature.productDescription}"/>
                        </tr>
                    </th:block>
                    <tr>
                        <td colspan="2">
                            <span>If your package includes a reward card we'll send you information on how to claim this separately.</span>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <!--</div>-->
                <hr class="hr"/>
                <p class ="heading2">
                    <b>2. Speed of the internet access service and remedies in case of problems</b>
                </p>
                <p class="summary2">
                    We predict your service will provide the following speeds:
                </p>
                <table id="cs_tbl2" class="cs_tbl table-bordered">
                    <thead>
                    <tr>
                        <td colspan="2" class="heading3"> Broadband Speeds </td>
                    </tr>
                    </thead>
                    <colgroup>
                        <col span="1" style="width: 40%;"/>
                    </colgroup>
                    <tbody>
                    <tr th:if="${!#strings.isEmpty(pcicsInfo.broadbandProduct.estimatedDownloadSpeedMin)
                        && !#strings.isEmpty(pcicsInfo.broadbandProduct.estimatedDownloadSpeedMax)}">
                        <td>Estimated Download Speed Range</td>
                        <td th:text="${pcicsInfo.broadbandProduct.estimatedDownloadSpeedMin}
                        + ' &ndash; '
                        + ${pcicsInfo.broadbandProduct.estimatedDownloadSpeedMax}"/>
                    </tr>
                    <tr th:if="${!#strings.isEmpty(pcicsInfo.broadbandProduct.estimatedUploadSpeedMin)
                        && !#strings.isEmpty(pcicsInfo.broadbandProduct.estimatedUploadSpeedMax)}">
                        <td>Estimated Upload Speed Range</td>
                        <td th:text="${pcicsInfo.broadbandProduct.estimatedUploadSpeedMin}
                        + ' &ndash; '
                        + ${pcicsInfo.broadbandProduct.estimatedUploadSpeedMax}"/>
                    </tr>
                    <tr th:if="${!#strings.isEmpty(pcicsInfo.broadbandProduct.minGuaranteedSpeed)}">
                        <td>Minimum Guaranteed Speed (Download)</td>
                        <td th:text="${pcicsInfo.broadbandProduct.minGuaranteedSpeed}"/>
                    </tr>
                    <tr th:if="${pcicsInfo.broadbandProduct.displayName != null}">
                        <td>Technology Type</td>
                        <td th:text="${pcicsInfo.broadbandProduct.getDisplayName()}"/>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <span>
                                To find out more about your broadband technology, please visit <a class="a_margin" href="https://www.plus.net/help/broadband/broadband-package-guide/">https://www.plus.net/help/broadband/broadband-package-guide/</a>
                                <br/>
                                <br/>
                                If you're regularly getting download speeds lower than estimated, let us know and we'll try to improve them.
                                In certain circumstances you'll be able to cancel your service early without paying a fee should speeds consistently
                                fall short of the Minimum Guaranteed Speed. You also have other legal options, see <a class="a_margin" href="https://www.citizensadvice.org.uk/">https://www.citizensadvice.org.uk/</a>.
                                Just so you know, we'll comply with all relevant Ofcom General Conditions and Voluntary Codes of Practice where we have agreed to that particular code. See our terms and conditions <a href="https://www.plus.net/help/legal/">https://www.plus.net/help/legal/</a> and <a href="https://www.plus.net/help/legal/ofcom-voluntary-speed-code-of-practice/">https://www.plus.net/help/legal/ofcom-voluntary-speed-code-of-practice/</a> for more information.
                                <br/>
                                <br/>
                                Our Code of Practice, see <a href="https://www.plus.net/help/legal/plusnet-code-of-practice/">https://www.plus.net/help/legal/plusnet-code-of-practice/</a> and our Customer Complaints
                                Code, see <a href="https://www.plus.net/help/legal/complaints-code-of-practice/">https://www.plus.net/help/legal/complaints-code-of-practice/</a> tell you how to get in touch to sort out a
                                problem and how we'll deal with any complaint or dispute. You may be able to take the matter to an alternative dispute
                                resolution service to get an independent opinion. More details are given in our Code of Practice, Customer Complaints
                                Code and our terms and conditions, see <a class="a_margin" href="https://www.plus.net/help/legal/">https://www.plus.net/help/legal/</a>.
                                <br/>
                                <br/>
                                Significant deviations from the respective advertised download and upload speeds could impact the exercise of the End-Users' rights laid down in Article 3(1) of Regulation (EU) 2015/2120 (the Open Internet Regulation).
                                <br/>
                            </span>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <hr class="hr"/>
                <p class ="heading1">
                    <b>3. Price</b>
                </p>
                <table id="cs_tbl3" class="cs_tbl table-bordered" >
                    <thead>
                    <tr>
                    <td colspan="2" class="heading3"><b>Monthly costs</b></td>
                    </tr>
                    </thead>
                    <colgroup>
                        <col span="1" style="width: 33%;"/>
                    </colgroup>
                    <tbody>
                    <tr>
                        <td><b>Broadband Package</b></td>
                        <td>
                            <p class="summary2">
                            <div>
                                <table class="custom-table">
                                    <tr>
                                        <td th:text="${pcicsInfo.broadbandProduct.productDisplayName}"></td>
                                        <td th:text="'&pound;' + ${pcicsInfo.broadbandProduct.discountedPrice} + ' a month'"></td>
                                    </tr>
                                    <!-- Check if futureDate1 and futurePrice1 are available -->
                                    <tr th:if="${pcicsInfo.broadbandProduct.futureDate1 != null && pcicsInfo.broadbandProduct.futurePrice1 != null}">
                                        <td th:text="'From ' + ${#dates.format(pcicsInfo.broadbandProduct.getFutureDate1AsDate(), 'dd MMMM yyyy')}"></td>
                                        <td th:text="'&pound;' + ${pcicsInfo.broadbandProduct.futurePrice1} + ' a month'"></td>
                                    </tr>
                                    <!-- Check if futureDate2 and futurePrice2 are available -->
                                    <tr th:if="${pcicsInfo.broadbandProduct.futureDate2 != null && pcicsInfo.broadbandProduct.futurePrice2 != null}">
                                        <td th:text="'From ' + ${#dates.format(pcicsInfo.broadbandProduct.getFutureDate2AsDate(), 'dd MMMM yyyy')}"></td>
                                        <td th:text="'&pound;' + ${pcicsInfo.broadbandProduct.futurePrice2} + ' a month'"></td>
                                    </tr>
                                </table>
                                <span>At the end of your minimum term your price will increase and we will write to you in advance to tell you what the new price will be. Our standard prices are set out in our </span>
                                <a href="https://www.plus.net/help/legal/plusnet-price-guide-for-residential-products/">price guide</a>
                            </div>
                            </p>
                        </td>
                    </tr>
                    <tr th:unless="${#lists.isEmpty(pcicsInfo.addOns) }">
                        <td><b>Add-Ons</b></td>
                        <td>
                            <span th:each="addOn, addOnStat : ${pcicsInfo.addOns}">
                                <span th:if="${addOnStat.last}">
                                    <span th:if="${addOn.c2mProductName == 'PlusnetProtect'}"
                                          th:text="'Access to Plusnet Protect powered by Norton, &pound;' + ${addOn.fullPrice} + ' a month for 2 years from activation'"></span>
                                    <span th:if="${addOn.c2mProductName != 'PlusnetProtect'}"
                                          th:text="${addOn.productDisplayName} + ' &pound;' + ${addOn.fullPrice} + ' a month'"></span>
                                </span>
                                <span th:if="${!addOnStat.last}">
                                    <span th:if="${addOn.c2mProductName == 'PlusnetProtect'}"
                                          th:text="'Access to Plusnet Protect powered by Norton, &pound;' + ${addOn.fullPrice} + ' a month for 2 years from activation, '"></span>
                                    <span th:if="${addOn.c2mProductName != 'PlusnetProtect'}"
                                          th:text="${addOn.productDisplayName} + ' &pound;' + ${addOn.fullPrice} + ' a month, '"></span>
                                </span>
                            </span>
                        </td>
                    </tr>

                    <tr th:unless="${pcicsInfo.lineRentalProduct == null}">
                        <td><b>Line Rental</b></td>
                        <td>
                            <p class="summary2">
                            <span th:text="${pcicsInfo.lineRentalProduct.productDisplayName}
                                    + ' &pound;'
                                    + ${pcicsInfo.lineRentalProduct.discountedPrice}
                                    + ' a month. At the end of your minimum commitment period your price will increase
                                       and we will write to you in advance to tell you what the new price will be.
                                       Our standard prices are set out in our '"/>
                            <a href="https://www.plus.net/help/legal/plusnet-price-guide-for-residential-products/">price
                                guide</a>
                            </p>
                        </td>
                    </tr>
                    <tr th:unless="${pcicsInfo.callPlanProduct == null}">
                        <td><b>Call Plan</b></td>
                        <td>
                            <span th:text="${pcicsInfo.callPlanProduct.productDisplayName}
                            + ' &pound;'
                            + ${pcicsInfo.callPlanProduct.fullPrice}
                            + ' a month'" />
                        </td>
                    </tr>
                    <th:block th:unless="${#lists.isEmpty(pcicsInfo.callFeatureList) }">
                        <tr>
                            <td><b>Call Features</b></td>
                            <td></td>
                        </tr>
                        <tr th:each="callFeature : ${pcicsInfo.callFeatureList}" th:if="${callFeature.fullPrice > 0}">
                            <td th:text="${callFeature.productDisplayName}"/>
                            <td th:text="'&pound;'
                            + ${callFeature.fullPrice}
                            + ' a month (not available in feature bundles).'"/>
                        </tr>
                        <tr th:if="${!#lists.isEmpty(pcicsInfo.callFeatureList)
                            && (#lists.contains(#numbers.listFormatCurrency(#lists.toList(pcicsInfo.callFeatureList.{fullPrice})), '$0.00')
                                || #lists.contains(#numbers.listFormatCurrency(#lists.toList(pcicsInfo.callFeatureList.{fullPrice})), '&pound;0.00'))}"
                        >
                            <td>You've taken the following features as part of a feature bundle (free of charge features also shown)</td>
                            <td>
                                <th:block th:each="callFeature : ${pcicsInfo.callFeatureList}" th:if="${!callFeature.fullPrice > 0}">
                                    <span th:text="${callFeature.productDisplayName}"/>
                                    <br/>
                                </th:block>
                            </td>
                        </tr>
                    </th:block>
                    <tr th:if="${pcicsInfo.totalCallFeatureBundlePrice != null}">
                        <td><b>Monthly cost for feature bundle</b></td>
                        <td th:text="'&pound;'
                            + ${pcicsInfo.totalCallFeatureBundlePrice}"/>
                    </tr>
                    <tr>
                        <td><b>Total Monthly Costs</b></td>
                        <td th:text="'&pound;'
                            + ${pcicsInfo.totalMonthlyCost}" />
                    </tr>
                    <tr>
                        <td colspan="2">
                            <span>* If you're due a discount, you'll see this on your bill</span>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <br/>
                <span  th:unless="${#lists.isEmpty(pcicsInfo.oocList)}">
                    <table id="cs_tbl4" class="cs_tbl table-bordered">
                        <thead>
                        <tr>
                            <td colspan="2" class="heading3"><b>One-off charges</b></td>
                        </tr>
                        </thead>
                        <colgroup>
                            <col span="1" style="width: 33%;"/>
                        </colgroup>
                        <tbody>

                        <tr th:each="oneOffCharge : ${pcicsInfo.oocList}" th:if="${oneOffCharge.oocPrice > 0}">
                            <td th:text="${oneOffCharge.oocName}" />
                            <td th:text="'&pound;'
                                + ${oneOffCharge.oocPrice}" />
                        </tr>
                        <tr>
                            <td>Total One-Off Charges</td>
                            <td th:text="'&pound;'
                                + ${pcicsInfo.totalOocPrice}" />
                                <!--#aggregates.sum(pcicsInfo.oocList.{fullPrice})}"/>-->
                        </tbody>
                    </table>
                    <br/>
                </span>
                <p class="summary2">
                    The monthly price for broadband will increase by <td th:text="'&pound;'+ 3" /> on 31 March each year.  All out of bundle charges will be increased by 5% (rounded to the nearest whole pence).
                    <br/><br/>
                    If you have a phone package as part of your service with us, and you are not moving to a new product, then we'll also increase the cost of international calls (calls from your landline to other countries) when our third-party partners put the costs up for us.
                    <br/><br/>
                    We may also increase any charges at any time. If we do, you may be able to end the service early without paying a cancellation charge.
                    <br/><br/>
                    See our terms and conditions <a href="https://www.plus.net/help/legal/">https://www.plus.net/help/legal/</a> and <a class="a_margin" href="https://www.plus.net/help/legal/about-annual-price-changes/">https://www.plus.net/help/legal/about-annual-price-changes/</a>
                </p>
                <hr class="hr"/>
                <p class ="heading1">
                    <b>4. Duration, renewal and termination</b>
                </p>
                <p class ="heading3">
                    <b>Duration:</b>
                </p>
                <table id="cs_tbl5" class="cs_tbl table-bordered" >
                    <colgroup>
                        <col span="1" style="width: 33%;"/>
                    </colgroup>
                    <tbody>
                    <tr>
                        <td><b>Product</b></td>
                        <td><b>Contract Length</b></td>
                    </tr>
                    <tr th:if="${pcicsInfo.broadbandProduct.contractDuration != null}">
                        <td>Broadband Package Name:<br>
                            <span th:text="${pcicsInfo.broadbandProduct.productDisplayName}"/>
                        </td>
                        <td th:text="${pcicsInfo.broadbandProduct.contractDuration}
                            + ' month Minimum Term'" />
                    </tr>

                    <tr th:unless="${#lists.isEmpty(pcicsInfo.addOns) }">
                        <td>Add-Ons </td>
                        <td>30 day Minimum Term</span>
                        </td>
                    </tr>
                    <tr th:if="${pcicsInfo.lineRentalProduct != null && pcicsInfo.lineRentalProduct.contractDuration != null}">
                        <td>Line Rental</td>
                        <td th:text="${pcicsInfo.lineRentalProduct.contractDuration}
                            + ' month Minimum Term'"/>
                    </tr>
                    <tr th:if="${pcicsInfo.callPlanProduct != null}">
                        <td>Call Plan</td>
                        <td>30 day Minimum Term</td>
                    </tr>
                    <tr th:unless="${#lists.isEmpty(pcicsInfo.callFeatureList) }">
                        <td>Call Features</td>
                        <td>30 day Minimum Term</td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <span>For most services, the minimum term starts when the service starts which is outlined in the terms and conditions. </span>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <p class ="heading3">
                    <b>Renewal:</b>
                </p>
                <p class="summary2">
                    Each service will last for a minimum amount of time (the minimum term), which is set out above. Once that ends, each service will continue until cancelled in accordance with the terms and conditions, see <a class="a_margin" href="https://www.plus.net/help/legal/">https://www.plus.net/help/legal/</a>.
                    <br/><br/>
                    If you have other contracts with us their minimum terms may not start or end at the same time as the minimum term for this contract.
                </p>
                <p class="summary2" th:if="${pcicsInfo.broadbandProduct.c2mProductName != 'UnlimitedFibre' and pcicsInfo.broadbandProduct.c2mProductName != 'FullFibre36'}">
                    If you choose to activate Plusnet Protect powered by Norton, you will need to register directly as a customer with Norton in order to download the software. Any renewal of this service beyond the initial 2-year free period will be negotiated directly with Norton and does not affect your contract with Plusnet.
                </p>
                <p class ="heading3">
                    <b>Termination:</b>
                </p>
                <p class="summary2">You can end each service using one of the ways set out in our terms and conditions. You'll need to contact us and give us 14 days' notice unless you're switching to another provider through an approved switching process, your agreement will end on the date your switch completes. Our website sets out details of how you may contact us to cancel.
                    <br/><br/>
                    You may have to pay us early termination charges if you end a service during the minimum term in accordance with our terms and conditions.
                    <br/><br/>
                    You may not need to pay early termination charges to end a service during the minimum term if we've made a change to it.
                    <br/><br/>
                    In some situations, this contract may form a linked contract with other contracts you take from us. If that's the case, in some situations where you're able to cancel a linked contract without having to pay early termination charges to leave early you may also be able to cancel this contract.
                    <br/><br/>
                    If you end a service (whether during the minimum term or after), you must return any loaned equipment and, if you don't return it within 60 days of ending the service, you'll be charged.
                    <br/><br/>
                    For more details on the above see section <b>7</b> of the Pre-Contract Information document and our terms and conditions, see <a class="a_margin" href="https://www.plus.net/help/legal/">https://www.plus.net/help/legal/</a>.
                </p>
                <hr class="hr"/>
                <p class ="heading2">
                    <b>5. Features for customers with disabilities</b>
                </p>
                <p class="summary2">
                    You can find out more about the products and help and advice available on our website, see <a class="a_margin" href="https://www.plus.net/help/legal/support-for-customers-with-disabilities/">https://www.plus.net/help/legal/support-for-customers-with-disabilities/</a>.
                </p>
                <hr class="hr"/>
                <p class ="heading2">
                    <b>6. Other relevant information</b>
                </p>
                <p class="summary2">For information on the right to cancel see section <b>10</b> of the Pre-Contract Information document.</p>
                <br/>
            </div>
        </div>
        <p class="pagebreak"/>
    </body>
</html>
