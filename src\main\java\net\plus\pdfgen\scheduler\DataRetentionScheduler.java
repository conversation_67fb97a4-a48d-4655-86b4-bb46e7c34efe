package net.plus.pdfgen.scheduler;

import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
@Setter
public class DataRetentionScheduler
{
    private PcicsCleanupTask pcicsCleanupTask;

    @Value("${data.rentention.pcics-pdf-cleanup-task-name}")
    private String taskName;

    public DataRetentionScheduler(PcicsCleanupTask pcicsCleanupTask)
    {
        this.pcicsCleanupTask = pcicsCleanupTask;
    }

    @Scheduled(cron = "${data.retention.pcics-pdf-cleanup-cron-expression}")
    @SchedulerLock(name = "${data.rentention.pcics-pdf-cleanup-task-name}",
                   lockAtLeastFor = "${data.retention.pcics-pdf-cleanup-lock-at-least-for}",
                   lockAtMostFor = "${data.retention.pcics-pdf-cleanup-lock-at-most-for}")
    public void schedulePdfgenCleanUpTask()
    {
        try
        {
            log.info("Start : Scheduled task [Task={}]", taskName);
            pcicsCleanupTask.run();
            log.info("End : Scheduled task successful [Task={}]", taskName);
        }
        catch (Exception e)
        {
            log.error("Scheduled task [ {} ] failed with Exception [ {} ].",
                      taskName,
                      e.fillInStackTrace().toString());
        }
    }
}
