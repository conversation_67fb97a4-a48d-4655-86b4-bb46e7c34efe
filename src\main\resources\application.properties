server.port=8089

pdf.dir.base.location=/share/pdfgen
pdf.dir.date.format=yyyy/MM/dd

template.path=static/pcics/templates/

pcics.dir.location=${pdf.dir.base.location}/pcics/
pcics.stream.url=https://plus.net/pci-terms?streaming-id

number.of.records.to.fetch=1000

#Coredb configuration
coredb-datasource.jdbcUrl=******************************************************************
coredb-datasource.username=pdfgenuser
coredb-datasource.password=${PDFGEN_COREDB_PASS}

#APPDB configuration
appdb-datasource.jdbcUrl=***********************************************************************
appdb-datasource.username=pdfgenuser
appdb-datasource.password=${PDFGEN_APPDB_PASS}

#Actuator configurations
management.endpoints.web.exposure.include=health,info

#Health Endpoint Configuration
management.endpoint.health.enabled=true
management.endpoint.health.show-details=always
management.endpoint.info.enabled=true

#Loging config
logging.level.net.plus.pdfgen=${LOG_LEVEL}
logging.include-query-string=false
logging.include-client-info=true
logging.include-headers=true

# Data Retention config
data.retention.pcics-pdf-cleanup-max-age-in-years=6
data.retention.pcics-invalid-pdf-cleanup-max-age-in-days=14
data.rentention.pcics-pdf-cleanup-task-name=DataRetentionPCICSPDFCleanup
data.retention.pcics-pdf-cleanup-cron-expression=0 0 3 * * *
data.retention.pcics-pdf-cleanup-lock-at-least-for=5m
data.retention.pcics-pdf-cleanup-lock-at-most-for=1h
data.retention.pcics-pdf-cleanup-default-lock-at-most-for=2h
spring.application.name=PcicsPdfgenApplication