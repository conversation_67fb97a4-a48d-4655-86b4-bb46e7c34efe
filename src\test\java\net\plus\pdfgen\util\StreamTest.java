package net.plus.pdfgen.util;

import org.junit.Assert;
import org.junit.Test;

public class StreamTest
{

    private String streamingUrl = "https://plus.net/pci-terms?streaming-id";

    private String streamingId = "0a49af44fb126c85d18e54a7a3fbd959";

    @Test
    public void shouldReturnUniqueId() throws Exception
    {
        String uuid1 = Stream.getId();
        String uuid2 = Stream.getId();
        Assert.assertNotEquals(uuid1, uuid2);
    }

    @Test
    public void shouldReturnValidStreamingUrl() throws Exception
    {
        String url = new Stream(streamingUrl).getUrl(streamingId);
        Assert.assertEquals(url, streamingUrl + "=" + streamingId);
    }
}
