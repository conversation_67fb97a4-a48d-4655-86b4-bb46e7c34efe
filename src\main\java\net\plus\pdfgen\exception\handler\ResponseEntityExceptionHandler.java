package net.plus.pdfgen.exception.handler;

import com.fasterxml.jackson.databind.exc.InvalidFormatException;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import net.plus.pdfgen.exception.*;
import net.plus.pdfgen.exception.error.ErrorCode;
import net.plus.pdfgen.exception.error.ErrorLoggingHelper;
import net.plus.pdfgen.model.ErrorResponseDto;

import org.springframework.beans.TypeMismatchException;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.servlet.NoHandlerFoundException;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolationException;

import java.time.DateTimeException;
import java.util.Optional;
import java.util.stream.Collectors;

@ControllerAdvice
@Slf4j
@AllArgsConstructor
public class ResponseEntityExceptionHandler
{
    private final ErrorLoggingHelper errorLoggingHelper;

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    @ResponseStatus(code = HttpStatus.BAD_REQUEST)
    protected ResponseEntity<ErrorResponseDto> handleMethodArgumentNotValid(HttpServletRequest request,
            MethodArgumentNotValidException ex)
    {
        String message=ErrorCode.INVALID_REQUEST;
        Optional<String> errorMessage = ex.getBindingResult()
                                         .getAllErrors()
                                         .stream()
                                         .map(DefaultMessageSourceResolvable::getDefaultMessage)
                                         .collect(Collectors.toSet())
                                         .stream().findFirst();

        if(errorMessage.isPresent())
        {
            message = errorMessage.get();
        }

        ErrorResponseDto errorResponseDto = new ErrorResponseDto(message);

        log.info("Id= [{}] - CODE= [{}] - MESSAGE= [{}] - REQUEST= [{}] - CAUSE= [{}]",
                 ex.getMessage(),
                 ErrorCode.INVALID_REQUEST,
                 errorResponseDto.getMessage(),
                 errorLoggingHelper.getRequestDetails(request),
                 ex.getCause()
        );

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponseDto);
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseBody
    @ResponseStatus(code = HttpStatus.BAD_REQUEST)
    public ResponseEntity<ErrorResponseDto> handleMissingServletRequestParameter(
            HttpServletRequest request,
            MissingServletRequestParameterException ex)
    {
        return logInfo(null, request, ex, ErrorCode.REQUEST_PARAMETER_MISSING, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(ServletRequestBindingException.class)
    @ResponseBody
    @ResponseStatus(code = HttpStatus.BAD_REQUEST)
    public ResponseEntity<ErrorResponseDto> handleServletRequestBindingException(
            HttpServletRequest request,
            ServletRequestBindingException ex)
    {
        return logInfo(null, request, ex, ErrorCode.HEADER_MISSING, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(BindException.class)
    @ResponseBody
    @ResponseStatus(code = HttpStatus.BAD_REQUEST)
    protected ResponseEntity<ErrorResponseDto> handleBindException(
            HttpServletRequest request,
            BindException ex)
    {
        return logInfo(null, request, ex, ErrorCode.INVALID_REQUEST, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(NoHandlerFoundException.class)
    @ResponseBody
    @ResponseStatus(code = HttpStatus.NOT_FOUND)
    protected ResponseEntity<ErrorResponseDto> handleNoHandlerFoundException(
            HttpServletRequest request,
            NoHandlerFoundException ex)
    {
        return logInfo(null, request, ex, ErrorCode.NOT_FOUND, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseBody
    @ResponseStatus(code = HttpStatus.BAD_REQUEST)
    public ResponseEntity<ErrorResponseDto> handleHttpMessageNotReadable(
            HttpServletRequest request,
            HttpMessageNotReadableException ex)
    {
        if (ex.getMostSpecificCause() instanceof DateTimeException)
        {
            return logInfo(null, request, ex, ErrorCode.INVALID_DATE_FORMAT, HttpStatus.BAD_REQUEST);
        }
        if (ex.getMostSpecificCause() instanceof InvalidFormatException)
        {
            return logInfo(null, request, ex, ErrorCode.INVALID_INPUT, HttpStatus.BAD_REQUEST);
        }

        return logInfo(null, request, ex, ErrorCode.INVALID_REQUEST, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseBody
    @ResponseStatus(code = HttpStatus.METHOD_NOT_ALLOWED)
    protected ResponseEntity<ErrorResponseDto> handleHttpRequestMethodNotSupported(
            HttpServletRequest request,
            HttpRequestMethodNotSupportedException ex)
    {
        return logInfo(null, request, ex, ErrorCode.METHOD_NOT_ALLOWED, HttpStatus.METHOD_NOT_ALLOWED);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseBody
    @ResponseStatus(code = HttpStatus.BAD_REQUEST)
    public ResponseEntity<ErrorResponseDto> handleConstraintViolation(
            HttpServletRequest request,
            final ConstraintViolationException ex)
    {
        return logInfo(null, request, ex, ErrorCode.CONSTRAINT_VIOLATION_EXCEPTION, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(TypeMismatchException.class)
    @ResponseBody
    @ResponseStatus(code = HttpStatus.BAD_REQUEST)
    protected ResponseEntity<ErrorResponseDto> handleTypeMismatch(HttpServletRequest request, TypeMismatchException ex)
    {
        return logInfo(null, request, ex, ErrorCode.INVALID_FIELD, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(StreamingIdNotFoundExeception.class)
    @ResponseBody
    @ResponseStatus(code = HttpStatus.NOT_FOUND)
    protected ResponseEntity<ErrorResponseDto> handleTypeMismatch(HttpServletRequest request,
            StreamingIdNotFoundExeception ex)
    {
        return logInfo(ex.getStreamingId(), request, ex, ErrorCode.STREAMING_ID_NOT_FOUND, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler({ PdfOperationException.class })
    @ResponseBody
    @ResponseStatus(code = HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ErrorResponseDto> handlePdfRetrievalFailure(HttpServletRequest request,
            PdfOperationException ex)
    {
        return logError(request,
                        ex,
                        ex.getStreamingId(),
                        ex.getCode(),
                        HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler({ SecurityException.class, SharedDirectoryCreationException.class })
    @ResponseBody
    @ResponseStatus(code = HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ErrorResponseDto> handleDirectoryCreationFailure(HttpServletRequest request, ApiException ex)
    {
        return logError(request,
                        ex,
                        ex.getStreamingId(),
                        ex.getCode(),
                        HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ExceptionHandler({ SqlExecException.class })
    @ResponseBody
    @ResponseStatus(code = HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ErrorResponseDto> handleCoredbQueryExecFailure(HttpServletRequest request,
            SqlExecException ex)
    {
        return logError(request,
                        ex,
                        ex.getStreamingId(),
                        ex.getCode(),
                        HttpStatus.INTERNAL_SERVER_ERROR);
    }

    private ResponseEntity<ErrorResponseDto> logInfo(
            String streamingId,
            HttpServletRequest request,
            Exception ex,
            String code,
            HttpStatus status)
    {
        ErrorResponseDto error = new ErrorResponseDto(code);
        log.info("Id= [{}] - StreamingId= [{}] - CODE= [{}] - MESSAGE= [{}] - CAUSE= [{}] - REQUEST= [{}]",
                 error.getId(),
                 streamingId,
                 code,
                 ex.getMessage(),
                 ex.getCause(),
                 errorLoggingHelper.getRequestDetails(request)
        );

        return ResponseEntity.status(status).body(error);
    }

    private ResponseEntity<ErrorResponseDto> logError(
            HttpServletRequest request,
            Exception ex,
            String streamingId,
            String code,
            HttpStatus status)
    {
        ErrorResponseDto errorResponseDto = new ErrorResponseDto(code);
        errorResponseDto.setId(streamingId);
        log.error("StreamingId= [{}] - CODE= [{}] - MESSAGE= [{}] - CAUSE= [{}] - REQUEST= [{}]",
                  streamingId,
                  code,
                  ex.getMessage(),
                  ex.getCause(),
                  errorLoggingHelper.getRequestDetails(request)
        );

        return ResponseEntity.status(status).body(errorResponseDto);
    }

}
