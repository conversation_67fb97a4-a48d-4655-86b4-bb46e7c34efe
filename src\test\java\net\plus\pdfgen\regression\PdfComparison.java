package net.plus.pdfgen.regression;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Optional;

import com.google.gson.Gson;

import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;

import de.redsix.pdfcompare.CompareResult;
import de.redsix.pdfcompare.PdfComparator;
import lombok.extern.slf4j.Slf4j;

import net.plus.pdfgen.model.PCICSResponseDto;

@Slf4j
public class PdfComparison
{
    private String url;

    private String responseLocation;

    private String requestLocation;

    private String resultLocation;

    private String baseDirLocation;

    private String dateFormat;

    private String journey;

    private String scenario;

    private final String JSON_EXTENSION = ".json";

    private final String PDF_EXTENSION = ".pdf";

    public PdfComparison(String url, String dirLocation, String fileLocation, String dateFormat)
    {
        this.url = url;
        this.baseDirLocation = dirLocation;
        this.requestLocation = fileLocation + "/request/";
        this.responseLocation = fileLocation + "/response/";
        this.resultLocation = fileLocation + "/result/";
        this.dateFormat = dateFormat;
    }

    public void initializeParameters(String journey, String scenario)
    {
        this.scenario = scenario;
        this.journey = journey;
    }

    public boolean generatePdfAndCompare()
    {
        Optional<ResponseEntity<String>> response = getPcicsHttpResponse();
        boolean isEqual = false;
        if (response.get() != null)
        {
            PCICSResponseDto pciResponse = getPcicsResponseDto(response);
            if (pciResponse != null && pciResponse.getStreamingId() != null)
            {
                Optional<String> actualPdfFilePath = getGeneratedPdfFilePath(pciResponse);
                isEqual = comparePDF(actualPdfFilePath.get());
            }
            else
            {
                log.error("Received incorrect response from PCICS application: {} ", pciResponse);
            }
        }
        return isEqual;
    }

    private Optional<ResponseEntity<String>> getPcicsHttpResponse()
    {
        String jsonRequestFilePath = getFilePath(journey, requestLocation, scenario, JSON_EXTENSION);
        String pdfCreationRequest = getPdfCreationJsonRequest(jsonRequestFilePath);
        return getHttpResponse(pdfCreationRequest);
    }

    private boolean comparePDF(String source){
        try {
            String destination = getFilePath(journey, responseLocation, scenario, PDF_EXTENSION);
            CompareResult compareResult = new PdfComparator(source, destination).compare();
            if (compareResult.isNotEqual()) {
                log.debug("Differences found in the generated pdf");
                checkAndCreateDirectory();
                String resultDir = getFilePath(journey, resultLocation, scenario, "");
                compareResult.writeTo(resultDir);
            }
            return compareResult.isEqual();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private Optional<String> getGeneratedPdfFilePath(PCICSResponseDto pciResponse)
    {
        SimpleDateFormat dateInFormat = new SimpleDateFormat(dateFormat);
        String baseDirectory =  baseDirLocation + dateInFormat.format(new Date()) + "/RES";
        Optional<String> file = Optional.of(baseDirectory + "/PCI_CS_" + pciResponse.getStreamingId() + PDF_EXTENSION);
        return file;
    }

    private PCICSResponseDto getPcicsResponseDto(Optional<ResponseEntity<String>> response)
    {
        String responseString = response.get().getBody();
        Gson gson = new Gson();
        PCICSResponseDto pciResponse =  gson.fromJson(responseString,PCICSResponseDto.class);
        return pciResponse;
    }

    private void checkAndCreateDirectory()
    {
        String resultDir = getFilePath(journey, resultLocation, "", "");
        File dir = new File(resultDir);
        if (!dir.exists())
        {
            if (dir.mkdirs())
            {
                log.debug("Created new directory : {}", resultDir);
            }
        }
    }

    private Optional<ResponseEntity<String>> getHttpResponse(String createPDFRequest)
    {
        TestRestTemplate restTemplate = new TestRestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.set(HttpHeaders.CONTENT_TYPE, "application/json");
        HttpEntity<String> entity = new HttpEntity<>(createPDFRequest, headers);
        Optional<ResponseEntity<String>> response =  Optional.of(restTemplate.exchange(url, HttpMethod.POST, entity, String.class));
        return response;
    }

    private String getFilePath(String journey, String location, String fileName, String extension)
    {
        return new StringBuffer(location)
                .append(journey)
                .append("/")
                .append(fileName)
                .append(extension)
                .toString();
    }

    private String getPdfCreationJsonRequest(String requestLocation){
        Optional<String> jsonRequest = Optional.empty();
        try {
            jsonRequest = Optional.of(new String(Files.readAllBytes(Paths.get(requestLocation))));
        }catch (IOException e) {
            log.error("Reading the Json Request file failed. Error : {}", e.getMessage(), e);
        }
        return trimRequest(jsonRequest.get());
    }

    private String trimRequest(String request){
        request.replaceAll(" ","");
        return request.replaceAll("\r\n","");
    }

}
