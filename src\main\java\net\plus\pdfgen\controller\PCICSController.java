package net.plus.pdfgen.controller;

import lombok.extern.slf4j.Slf4j;

import net.plus.pdfgen.model.PCICSRequestDto;
import net.plus.pdfgen.model.PCICSResponseDto;
import net.plus.pdfgen.service.PCICSService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.hateoas.EntityModel;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

import net.plus.pdfgen.model.ErrorResponseDto;
import net.plus.pdfgen.model.PCICSStatusDto;

import javax.validation.Valid;

@RestController
@RequestMapping("/pdfgen/v1/pcics")
@Tag(name = "Pre Contract Information and Contract Summary",
     description = "Manage PCICS PDF generation")
@Slf4j
public class PCICSController
{

    private final PCICSService pcicsService;

    @Autowired
    public PCICSController(PCICSService pcicsService)
    {
        this.pcicsService = pcicsService;
    }

    @GetMapping(path = "/{streamingId}")
    @Operation(
            description = "Retrieves the PCICS PDF",
            tags = { "Pre Contract Information and Contract Summary" }
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "400",
                         description = "<ERROR_DESCRIPTION>",
                         content = @Content(
                                 mediaType = "application/json",
                                 schema = @Schema(implementation = ErrorResponseDto.class)
                         )),
            @ApiResponse(responseCode = "404",
                         description = "Streaming ID not found",
                         content = @Content(
                                 mediaType = "application/json",
                                 schema = @Schema(implementation = ErrorResponseDto.class)
                         )),
            @ApiResponse(responseCode = "500",
                         description = "Internal server error, <ERROR_DESCRIPTION>",
                         content = @Content(
                                 mediaType = "application/json",
                                 schema = @Schema(implementation = ErrorResponseDto.class)
                         ))
    })
    public ResponseEntity<byte[]> retrievePdf(
            @PathVariable
                    String streamingId)
    {
        log.info("Entry: PCICSController.retrievePdf(), streamingId = {} ", streamingId);
        byte[] pdfContent = pcicsService.retrievePdf(streamingId);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("application/pdf"));
        String filename = "pcics_" + streamingId + ".pdf";
        headers.setContentDispositionFormData(filename, filename);
        headers.setCacheControl("must-revalidate, post-check=0, pre-check=0");

        ResponseEntity<byte[]> response = new ResponseEntity(
                pdfContent, headers, HttpStatus.OK);

        log.info("Exit: PCICSController.retrievePdf()");
        return response;
    }

    @PostMapping(produces = { "application/json" })
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(
            description = "Generates the PCICS PDF ",
            tags = { "Pre Contract Information and Contract Summary" }
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "400",
                         description = "<ERROR_DESCRIPTION>",
                         content = @Content(
                                 mediaType = "application/json",
                                 schema = @Schema(implementation = ErrorResponseDto.class)
                         )),
            @ApiResponse(responseCode = "500",
                         description = "Internal server error, <ERROR_DESCRIPTION>",
                         content = @Content(
                                 mediaType = "application/json",
                                 schema = @Schema(implementation = ErrorResponseDto.class)
                         )),
            @ApiResponse(responseCode = "201",
                         description = "CREATED",
                         content = @Content(mediaType = "application/json",
                                            schema = @Schema(implementation = PCICSResponseDto.class)))
    })
    public ResponseEntity createPdf(@Valid
    @RequestBody
            PCICSRequestDto pcicsInfo)
    {
        log.info("Entry: PCICSController.createPdf(), Request = {}",
                 pcicsInfo.toString().replaceAll("[\r\n]", ""));
        PCICSResponseDto response = pcicsService.generatePCICS(pcicsInfo);
        log.info("Exit: PCICSController.createPdf(), Response = {}", response.toString().replaceAll("[\r\n]", ""));
        return ResponseEntity.status(HttpStatus.CREATED).body(EntityModel.of(response));
    }

    @PutMapping(path = "/{streamingId}")
    @ResponseStatus(HttpStatus.OK)
    @Operation(
            description = "Updates the PCICS PDF status in Core DB",
            tags = { "Pre Contract Information and Contract Summary" }
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "404",
                         description = "Streaming ID not found",
                         content = @Content(
                                 mediaType = "application/json",
                                 schema = @Schema(implementation = ErrorResponseDto.class)
                         )),
            @ApiResponse(responseCode = "400",
                         description = "<ERROR_DESCRIPTION>",
                         content = @Content(
                                 mediaType = "application/json",
                                 schema = @Schema(implementation = ErrorResponseDto.class)
                         )),
            @ApiResponse(responseCode = "500",
                         description = "Internal server error, <ERROR_DESCRIPTION>",
                         content = @Content(
                                 mediaType = "application/json",
                                 schema = @Schema(implementation = ErrorResponseDto.class)
                         )),
            @ApiResponse(responseCode = "200",
                         description = "OK"
            )
    })
    public ResponseEntity updateJourneyCompletionStatus(
            @PathVariable
                    String streamingId,
            @Valid
            @RequestBody
                    PCICSStatusDto statusDto)
    {
        log.info("Entry: PCICSController.updateJourneyCompletionStatus(), streamingId = {} and PCICSStatusDto = {}",
                 streamingId,
                 statusDto);
        pcicsService.updateJourneyCompletionStatus(streamingId, statusDto);
        log.info("Exit: PCICSController.updateJourneyCompletionStatus()");
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
