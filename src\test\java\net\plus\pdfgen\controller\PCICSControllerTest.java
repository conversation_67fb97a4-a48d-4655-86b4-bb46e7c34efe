package net.plus.pdfgen.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import net.plus.pdfgen.controller.PCICSController;
import net.plus.pdfgen.model.PCICSRequestDto;
import net.plus.pdfgen.model.PCICSResponseDto;
import net.plus.pdfgen.service.PCICSService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.RequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Optional;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ContextConfiguration(classes = PCICSController.class)
@AutoConfigureMockMvc
@WebMvcTest(value = PCICSController.class)
@ActiveProfiles("test")
@Slf4j
public class PCICSControllerTest {
    @MockBean
    private PCICSService pcicsService;

    @MockBean
    private PCICSController pcicsController;
    @Autowired
    private MockMvc mockMvc;
    private PCICSRequestDto pcicsRequestDto;
    @BeforeEach
    public void setUp()
    {
        mockSampleRequest();
    }

    @Test
    public void createPdfTestSuccess() throws Exception {
        PCICSResponseDto respo = PCICSResponseDto.builder().build();
        when(pcicsService.generatePCICS(pcicsRequestDto)).thenReturn(respo);

        RequestBuilder requestBuilder = MockMvcRequestBuilders.post(
                "/pdfgen/v1/pcics").content(asJsonString(pcicsRequestDto))
                .contentType(MediaType.APPLICATION_JSON).accept(
                MediaType.APPLICATION_JSON);


        this.mockMvc.perform(requestBuilder).andExpect(status().isCreated());

    }

    @Test
    public void createPdfTestFailure() throws Exception {
        PCICSResponseDto respo = PCICSResponseDto.builder().build();
        when(pcicsService.generatePCICS(pcicsRequestDto)).thenReturn(respo);

        RequestBuilder requestBuilder = MockMvcRequestBuilders.post(
                        "/pdfgen/v1/pcics").content(pcicsRequestDto.toString())
                .contentType(MediaType.APPLICATION_JSON).accept(
                        MediaType.APPLICATION_JSON);


        this.mockMvc.perform(requestBuilder).andExpect(status().is4xxClientError());
    }

    public static String asJsonString(Object requestDto){
        try{
            return new ObjectMapper().writeValueAsString(requestDto);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public void mockSampleRequest(){
        String createPDFRequest = getTheParseInputRequest();
        createPDFRequest = createPDFRequest.replaceAll(" \r\n", "");
        Gson gson = new Gson();
        pcicsRequestDto = gson.fromJson(createPDFRequest, PCICSRequestDto.class);
    }

    private String getTheParseInputRequest() {
        final String jsonRequestFileName = "./src/test/resources/input/CreatePdfRequest.json";
        Optional<String> parsedRequest = Optional.empty();
        try {
            parsedRequest = Optional.of(new String(Files.readAllBytes(Paths.get(jsonRequestFileName))));
        } catch (IOException e) {
            log.error("Reading the Json Request from the file is failed");
        }
        return parsedRequest.orElse(null);
    }
}
