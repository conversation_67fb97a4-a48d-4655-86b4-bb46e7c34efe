package net.plus.pdfgen.exception.error;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ErrorCode
{
    public static final String INVALID_FIELD = "Invalid field";
    public static final String REQUEST_PARAMETER_MISSING = "Missing request parameter";
    public static final String HEADER_MISSING = "Header missing";
    public static final String INVALID_REQUEST = "Invalid request";
    public static final String NOT_FOUND = "Not found";
    public static final String INVALID_DATE_FORMAT = "Invalid date format";
    public static final String METHOD_NOT_ALLOWED = "Method not allowed";
    public static final String CONSTRAINT_VIOLATION_EXCEPTION = "Constraint violation exception";

    public static final String CREATE_NEW_DIR_FAILURE
            = "Internal server error, directory creation failed";
    public static final String COREDB_QUERY_EXEC_FAILURE = "Internal server error,DB query execution failed";
    public static final String RETRIEVE_PDF_FAILURE
            = "Internal Server Error, PDF retrieval failed";
    public static final String CREATE_PDF_FAILURE
            = "Internal Server Error, PDF creation failed";
    public static final String STREAMING_ID_NOT_FOUND = "Streaming ID not found";
    public static final String INVALID_INPUT = "Invalid input";

    public static final String MISSING_JOURNEYCONTEXT = "JourneyContext is missing";
    public static final String INVALID_JOURNEYCONTEXT = "Invalid JourneyContext";
    public static final String MISSING_CUSTOMERTYPE = "CustomerType is missing";
    public static final String INVALID_CUSTOMERTYPE = "Invalid CustomerType";
    public static final String MISSING_TAXTREATMENT = "TaxTreatment is missing";
    public static final String INVALID_TAXTREATMENT = "Invalid TaxTreatment";
    public static final String MISSING_BROADBANDPRODUCT = "BroadbandProduct details missing";
    public static final String MISSING_TOTALMONTHLYCOST = "TotalMonthlyCost is missing";
    public static final String MISSING_PRODUCT_DISPLAY_NAME = "productDisplayName is missing";
    public static final String MISSING_C2M_PRODUCTNAME = "c2mProductName is missing";

    public static final String MISSING_PRODUCTPRICE = "Product fullPrice is missing";
    public static final String MISSING_DISCOUNTEDPRICE = "Product discountedPrice is missing";
    public static final String MISSING_OOCNAME = "OocName is missing";
    public static final String MISSING_OOCPRICE = "OocPrice is missing";
    public static final String MISSING_DELIVERYTYPE = "DeliveryType is missing";
    public static final String MISSING_DELIVERY_COMMS = "Delivery comms status is missing";
    public static final String MISSING_ACCOUNTID = "accountId is missing";
    public static final String VALUEOFENUM_DEFAULT_MESSAGE = "Input should contain accepted values only";

}
